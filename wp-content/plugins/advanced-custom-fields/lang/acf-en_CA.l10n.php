<?php
return ['domain'=>NULL,'plural-forms'=>NULL,'language'=>'en_CA','project-id-version'=>'Advanced Custom Fields','pot-creation-date'=>'2025-07-21T18:41:16+00:00','po-revision-date'=>'2024-06-27T14:24:00+00:00','x-generator'=>'gettext','messages'=>['\'%s\' is not a valid email address'=>'\'%s\' is not a valid email address','Color value'=>'Colour value','Select default color'=>'Select default colour','Clear color'=>'Clear colour','Blocks'=>'Blocks','Options'=>'Options','Users'=>'Users','Menu items'=>'Menu items','Widgets'=>'Widgets','Attachments'=>'Attachments','Taxonomies'=>'Taxonomies','Posts'=>'Posts','Last updated: %s'=>'Last updated: %s','Invalid field group parameter(s).'=>'Invalid field group parameter(s).','Awaiting save'=>'Awaiting save','Saved'=>'Saved','Import'=>'Import','Review changes'=>'Review changes','Located in: %s'=>'Located in: %s','Located in plugin: %s'=>'Located in plugin: %s','Located in theme: %s'=>'Located in theme: %s','Various'=>'Various','Sync changes'=>'Sync changes','Loading diff'=>'Loading diff','Review local JSON changes'=>'Review local JSON changes','Visit website'=>'Visit website','View details'=>'View details','Version %s'=>'Version %s','Information'=>'Information','<a href="%s" target="_blank">Help Desk</a>. The support professionals on our Help Desk will assist with your more in depth, technical challenges.'=>'<a href="%s" target="_blank">Help Desk</a>. The support professionals on our Help Desk will assist with your more in depth, technical challenges.','<a href="%s" target="_blank">Documentation</a>. Our extensive documentation contains references and guides for most situations you may encounter.'=>'<a href="%s" target="_blank">Documentation</a>. Our extensive documentation contains references and guides for most situations you may encounter.','We are fanatical about support, and want you to get the best out of your website with ACF. If you run into any difficulties, there are several places you can find help:'=>'We are fanatical about support, and want you to get the best out of your website with ACF. If you run into any difficulties, there are several places you can find help:','Help & Support'=>'Help & Support','Please use the Help & Support tab to get in touch should you find yourself requiring assistance.'=>'Please use the Help & Support tab to get in touch should you find yourself requiring assistance.','Before creating your first Field Group, we recommend first reading our <a href="%s" target="_blank">Getting started</a> guide to familiarize yourself with the plugin\'s philosophy and best practises.'=>'Before creating your first Field Group, we recommend first reading our <a href="%s" target="_blank">Getting started</a> guide to familiarize yourself with the plugin\'s philosophy and best practises.','The Advanced Custom Fields plugin provides a visual form builder to customize WordPress edit screens with extra fields, and an intuitive API to display custom field values in any theme template file.'=>'The Advanced Custom Fields plugin provides a visual form builder to customize WordPress edit screens with extra fields, and an intuitive API to display custom field values in any theme template file.','Overview'=>'Overview','Location type "%s" is already registered.'=>'Location type "%s" is already registered.','Class "%s" does not exist.'=>'Class "%s" does not exist.','Invalid nonce.'=>'Invalid nonce.','Error loading field.'=>'Error loading field.','Location not found: %s'=>'Location not found: %s','<strong>Error</strong>: %s'=>'<strong>Error</strong>: %s','Widget'=>'Widget','User Role'=>'User Role','Comment'=>'Comment','Post Format'=>'Post Format','Menu Item'=>'Menu Item','Post Status'=>'Post Status','Menus'=>'Menus','Menu Locations'=>'Menu Locations','Menu'=>'Menu','Post Taxonomy'=>'Post Taxonomy','Child Page (has parent)'=>'Child Page (has parent)','Parent Page (has children)'=>'Parent Page (has children)','Top Level Page (no parent)'=>'Top Level Page (no parent)','Posts Page'=>'Posts Page','Front Page'=>'Front Page','Page Type'=>'Page Type','Viewing back end'=>'Viewing back end','Viewing front end'=>'Viewing front end','Logged in'=>'Logged in','Current User'=>'Current User','Page Template'=>'Page Template','Register'=>'Register','Add / Edit'=>'Add / Edit','User Form'=>'User Form','Page Parent'=>'Page Parent','Super Admin'=>'Super Admin','Current User Role'=>'Current User Role','Default Template'=>'Default Template','Post Template'=>'Post Template','Post Category'=>'Post Category','All %s formats'=>'All %s formats','Attachment'=>'Attachment','%s value is required'=>'%s value is required','Show this field if'=>'Show this field if','Conditional Logic'=>'Conditional Logic','and'=>'and','Local JSON'=>'Local JSON','Clone Field'=>'Clone Field','Please also check all premium add-ons (%s) are updated to the latest version.'=>'Please also check all premium add-ons (%s) are updated to the latest version.','This version contains improvements to your database and requires an upgrade.'=>'This version contains improvements to your database and requires an upgrade.','Thank you for updating to %1$s v%2$s!'=>'Thank you for updating to %1$s v%2$s!','Database Upgrade Required'=>'Database Upgrade Required','Options Page'=>'Options Page','Gallery'=>'Gallery','Flexible Content'=>'Flexible Content','Repeater'=>'Repeater','Back to all tools'=>'Back to all tools','If multiple field groups appear on an edit screen, the first field group\'s options will be used (the one with the lowest order number)'=>'If multiple field groups appear on an edit screen, the first field group\'s options will be used (the one with the lowest order number)','<b>Select</b> items to <b>hide</b> them from the edit screen.'=>'<b>Select</b> items to <b>hide</b> them from the edit screen.','Hide on screen'=>'Hide on screen','Send Trackbacks'=>'Send Trackbacks','Tags'=>'Tags','Categories'=>'Categories','Page Attributes'=>'Page Attributes','Format'=>'Format','Author'=>'Author','Slug'=>'Slug','Revisions'=>'Revisions','Comments'=>'Comments','Discussion'=>'Discussion','Excerpt'=>'Excerpt','Content Editor'=>'Content Editor','Permalink'=>'Permalink','Shown in field group list'=>'Shown in field group list','Field groups with a lower order will appear first'=>'Field groups with a lower order will appear first','Order No.'=>'Order No.','Below fields'=>'Below fields','Below labels'=>'Below labels','Side'=>'Side','Normal (after content)'=>'Normal (after content)','High (after title)'=>'High (after title)','Position'=>'Position','Seamless (no metabox)'=>'Seamless (no metabox)','Standard (WP metabox)'=>'Standard (WP metabox)','Style'=>'Style','Type'=>'Type','Key'=>'Key','Order'=>'Order','Close Field'=>'Close Field','id'=>'id','class'=>'class','width'=>'width','Wrapper Attributes'=>'Wrapper Attributes','Instructions'=>'Instructions','Field Type'=>'Field Type','Single word, no spaces. Underscores and dashes allowed'=>'Single word, no spaces. Underscores and dashes allowed','Field Name'=>'Field Name','This is the name which will appear on the EDIT page'=>'This is the name which will appear on the EDIT page','Field Label'=>'Field Label','Delete'=>'Delete','Delete field'=>'Delete field','Move'=>'Move','Move field to another group'=>'Move field to another group','Duplicate field'=>'Duplicate field','Edit field'=>'Edit field','Drag to reorder'=>'Drag to reorder','Show this field group if'=>'Show this field group if','No updates available.'=>'No updates available.','Database upgrade complete. <a href="%s">See what\'s new</a>'=>'Database upgrade complete. <a href="%s">See what\'s new</a>','Reading upgrade tasks...'=>'Reading upgrade tasks…','Upgrade failed.'=>'Upgrade failed.','Upgrade complete.'=>'Upgrade complete.','Upgrading data to version %s'=>'Upgrading data to version %s','It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?'=>'It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?','Please select at least one site to upgrade.'=>'Please select at least one site to upgrade.','Database Upgrade complete. <a href="%s">Return to network dashboard</a>'=>'Database Upgrade complete. <a href="%s">Return to network dashboard</a>','Site is up to date'=>'Site is up to date','Site requires database upgrade from %1$s to %2$s'=>'Site requires database upgrade from %1$s to %2$s','Site'=>'Site','Upgrade Sites'=>'Upgrade Sites','The following sites require a DB upgrade. Check the ones you want to update and then click %s.'=>'The following sites require a DB upgrade. Check the ones you want to update and then click %s.','Add rule group'=>'Add rule group','Create a set of rules to determine which edit screens will use these advanced custom fields'=>'Create a set of rules to determine which edit screens will use these advanced custom fields','Rules'=>'Rules','Copied'=>'Copied','Copy to clipboard'=>'Copy to clipboard','Select Field Groups'=>'Select Field Groups','No field groups selected'=>'No field groups selected','Generate PHP'=>'Generate PHP','Export Field Groups'=>'Export Field Groups','Import file empty'=>'Import file empty','Incorrect file type'=>'Incorrect file type','Error uploading file. Please try again'=>'Error uploading file. Please try again','Import Field Groups'=>'Import Field Groups','Sync'=>'Sync','Select %s'=>'Select %s','Duplicate'=>'Duplicate','Duplicate this item'=>'Duplicate this item','Documentation'=>'Documentation','Description'=>'Description','Sync available'=>'Sync available','Field group duplicated.'=>'Field group duplicated.' . "\0" . '%s field groups duplicated.','Active <span class="count">(%s)</span>'=>'Active <span class="count">(%s)</span>' . "\0" . 'Active <span class="count">(%s)</span>','Review sites & upgrade'=>'Review sites & upgrade','Upgrade Database'=>'Upgrade Database','Custom Fields'=>'Custom Fields','Move Field'=>'Move Field','Please select the destination for this field'=>'Please select the destination for this field','The %1$s field can now be found in the %2$s field group'=>'The %1$s field can now be found in the %2$s field group','Move Complete.'=>'Move Complete.','Active'=>'Active','Field Keys'=>'Field Keys','Settings'=>'Settings','Location'=>'Location','Null'=>'Null','copy'=>'copy','(this field)'=>'(this field)','Checked'=>'Checked','Move Custom Field'=>'Move Custom Field','No toggle fields available'=>'No toggle fields available','Field group title is required'=>'Field group title is required','This field cannot be moved until its changes have been saved'=>'This field cannot be moved until its changes have been saved','The string "field_" may not be used at the start of a field name'=>'The string "field_" may not be used at the start of a field name','Field group draft updated.'=>'Field group draft updated.','Field group scheduled for.'=>'Field group scheduled for.','Field group submitted.'=>'Field group submitted.','Field group saved.'=>'Field group saved.','Field group published.'=>'Field group published.','Field group deleted.'=>'Field group deleted.','Field group updated.'=>'Field group updated.','Tools'=>'Tools','is not equal to'=>'is not equal to','is equal to'=>'is equal to','Forms'=>'Forms','Page'=>'Page','Post'=>'Post','Relational'=>'Relational','Choice'=>'Choice','Basic'=>'Basic','Unknown'=>'Unknown','Field type does not exist'=>'Field type does not exist','Spam Detected'=>'Spam Detected','Post updated'=>'Post updated','Update'=>'Update','Validate Email'=>'Validate Email','Content'=>'Content','Title'=>'Title','Edit field group'=>'Edit field group','Selection is less than'=>'Selection is less than','Selection is greater than'=>'Selection is greater than','Value is less than'=>'Value is less than','Value is greater than'=>'Value is greater than','Value contains'=>'Value contains','Value matches pattern'=>'Value matches pattern','Value is not equal to'=>'Value is not equal to','Value is equal to'=>'Value is equal to','Has no value'=>'Has no value','Has any value'=>'Has any value','Cancel'=>'Cancel','Are you sure?'=>'Are you sure?','%d fields require attention'=>'%d fields require attention','1 field requires attention'=>'1 field requires attention','Validation failed'=>'Validation failed','Validation successful'=>'Validation successful','Restricted'=>'Restricted','Collapse Details'=>'Collapse Details','Expand Details'=>'Expand Details','Uploaded to this post'=>'Uploaded to this post','verbUpdate'=>'Update','verbEdit'=>'Edit','The changes you made will be lost if you navigate away from this page'=>'The changes you made will be lost if you navigate away from this page','File type must be %s.'=>'File type must be %s.','or'=>'or','File size must not exceed %s.'=>'File size must not exceed %s.','File size must be at least %s.'=>'File size must be at least %s.','Image height must not exceed %dpx.'=>'Image height must not exceed %dpx.','Image height must be at least %dpx.'=>'Image height must be at least %dpx.','Image width must not exceed %dpx.'=>'Image width must not exceed %dpx.','Image width must be at least %dpx.'=>'Image width must be at least %dpx.','(no title)'=>'(no title)','Full Size'=>'Full Size','Large'=>'Large','Medium'=>'Medium','Thumbnail'=>'Thumbnail','(no label)'=>'(no label)','Sets the textarea height'=>'Sets the textarea height','Rows'=>'Rows','Text Area'=>'Text Area','Prepend an extra checkbox to toggle all choices'=>'Prepend an extra checkbox to toggle all choices','Save \'custom\' values to the field\'s choices'=>'Save \'custom\' values to the field\'s choices','Allow \'custom\' values to be added'=>'Allow \'custom\' values to be added','Add new choice'=>'Add new choice','Toggle All'=>'Toggle All','Allow Archives URLs'=>'Allow Archives URLs','Archives'=>'Archives','Page Link'=>'Page Link','Add'=>'Add','Name'=>'Name','%s added'=>'%s added','%s already exists'=>'%s already exists','User unable to add new %s'=>'User unable to add new %s','Term ID'=>'Term ID','Term Object'=>'Term Object','Load value from posts terms'=>'Load value from posts terms','Load Terms'=>'Load Terms','Connect selected terms to the post'=>'Connect selected terms to the post','Save Terms'=>'Save Terms','Allow new terms to be created whilst editing'=>'Allow new terms to be created whilst editing','Create Terms'=>'Create Terms','Radio Buttons'=>'Radio Buttons','Single Value'=>'Single Value','Multi Select'=>'Multi Select','Checkbox'=>'Checkbox','Multiple Values'=>'Multiple Values','Select the appearance of this field'=>'Select the appearance of this field','Appearance'=>'Appearance','Select the taxonomy to be displayed'=>'Select the taxonomy to be displayed','Value must be equal to or lower than %d'=>'Value must be equal to or lower than %d','Value must be equal to or higher than %d'=>'Value must be equal to or higher than %d','Value must be a number'=>'Value must be a number','Number'=>'Number','Save \'other\' values to the field\'s choices'=>'Save \'other\' values to the field\'s choices','Add \'other\' choice to allow for custom values'=>'Add \'other\' choice to allow for custom values','Other'=>'Other','Radio Button'=>'Radio Button','Define an endpoint for the previous accordion to stop. This accordion will not be visible.'=>'Define an endpoint for the previous accordion to stop. This accordion will not be visible.','Allow this accordion to open without closing others.'=>'Allow this accordion to open without closing others.','Display this accordion as open on page load.'=>'Display this accordion as open on page load.','Open'=>'Open','Accordion'=>'Accordion','Restrict which files can be uploaded'=>'Restrict which files can be uploaded','File ID'=>'File ID','File URL'=>'File URL','File Array'=>'File Array','Add File'=>'Add File','No file selected'=>'No file selected','File name'=>'File name','Update File'=>'Update File','Edit File'=>'Edit File','Select File'=>'Select File','File'=>'File','Password'=>'Password','Specify the value returned'=>'Specify the value returned','Use AJAX to lazy load choices?'=>'Use AJAX to lazy load choices?','Enter each default value on a new line'=>'Enter each default value on a new line','verbSelect'=>'Select','Select2 JS load_failLoading failed'=>'Loading failed','Select2 JS searchingSearching&hellip;'=>'Searching&hellip;','Select2 JS load_moreLoading more results&hellip;'=>'Loading more results&hellip;','Select2 JS selection_too_long_nYou can only select %d items'=>'You can only select %d items','Select2 JS selection_too_long_1You can only select 1 item'=>'You can only select 1 item','Select2 JS input_too_long_nPlease delete %d characters'=>'Please delete %d characters','Select2 JS input_too_long_1Please delete 1 character'=>'Please delete 1 character','Select2 JS input_too_short_nPlease enter %d or more characters'=>'Please enter %d or more characters','Select2 JS input_too_short_1Please enter 1 or more characters'=>'Please enter 1 or more characters','Select2 JS matches_0No matches found'=>'No matches found','Select2 JS matches_n%d results are available, use up and down arrow keys to navigate.'=>'%d results are available, use up and down arrow keys to navigate.','Select2 JS matches_1One result is available, press enter to select it.'=>'One result is available, press enter to select it.','nounSelect'=>'Select','User ID'=>'User ID','User Object'=>'User Object','User Array'=>'User Array','All user roles'=>'All user roles','User'=>'User','Separator'=>'Separator','Select Color'=>'Select Colour','Default'=>'Default','Clear'=>'Clear','Color Picker'=>'Colour Picker','Date Time Picker JS pmTextShortP'=>'P','Date Time Picker JS pmTextPM'=>'PM','Date Time Picker JS amTextShortA'=>'A','Date Time Picker JS amTextAM'=>'AM','Date Time Picker JS selectTextSelect'=>'Select','Date Time Picker JS closeTextDone'=>'Done','Date Time Picker JS currentTextNow'=>'Now','Date Time Picker JS timezoneTextTime Zone'=>'Time Zone','Date Time Picker JS microsecTextMicrosecond'=>'Microsecond','Date Time Picker JS millisecTextMillisecond'=>'Millisecond','Date Time Picker JS secondTextSecond'=>'Second','Date Time Picker JS minuteTextMinute'=>'Minute','Date Time Picker JS hourTextHour'=>'Hour','Date Time Picker JS timeTextTime'=>'Time','Date Time Picker JS timeOnlyTitleChoose Time'=>'Choose Time','Date Time Picker'=>'Date Time Picker','Endpoint'=>'Endpoint','Left aligned'=>'Left aligned','Top aligned'=>'Top aligned','Placement'=>'Placement','Tab'=>'Tab','Value must be a valid URL'=>'Value must be a valid URL','Link URL'=>'Link URL','Link Array'=>'Link Array','Opens in a new window/tab'=>'Opens in a new window/tab','Select Link'=>'Select Link','Link'=>'Link','Email'=>'Email','Step Size'=>'Step Size','Maximum Value'=>'Maximum Value','Minimum Value'=>'Minimum Value','Range'=>'Range','Both (Array)'=>'Both (Array)','Label'=>'Label','Value'=>'Value','Vertical'=>'Vertical','Horizontal'=>'Horizontal','red : Red'=>'red : Red','For more control, you may specify both a value and label like this:'=>'For more control, you may specify both a value and label like this:','Enter each choice on a new line.'=>'Enter each choice on a new line.','Choices'=>'Choices','Button Group'=>'Button Group','Parent'=>'Parent','TinyMCE will not be initialized until field is clicked'=>'TinyMCE will not be initialized until field is clicked','Toolbar'=>'Toolbar','Text Only'=>'Text Only','Visual Only'=>'Visual Only','Visual & Text'=>'Visual & Text','Tabs'=>'Tabs','Click to initialize TinyMCE'=>'Click to initialize TinyMCE','Name for the Text editor tab (formerly HTML)Text'=>'Text','Visual'=>'Visual','Value must not exceed %d characters'=>'Value must not exceed %d characters','Leave blank for no limit'=>'Leave blank for no limit','Character Limit'=>'Character Limit','Appears after the input'=>'Appears after the input','Append'=>'Append','Appears before the input'=>'Appears before the input','Prepend'=>'Prepend','Appears within the input'=>'Appears within the input','Placeholder Text'=>'Placeholder Text','Appears when creating a new post'=>'Appears when creating a new post','Text'=>'Text','%1$s requires at least %2$s selection'=>'%1$s requires at least %2$s selection' . "\0" . '%1$s requires at least %2$s selections','Post ID'=>'Post ID','Post Object'=>'Post Object','Featured Image'=>'Featured Image','Selected elements will be displayed in each result'=>'Selected elements will be displayed in each result','Elements'=>'Elements','Taxonomy'=>'Taxonomy','Post Type'=>'Post Type','Filters'=>'Filters','All taxonomies'=>'All taxonomies','Filter by Taxonomy'=>'Filter by Taxonomy','All post types'=>'All post types','Filter by Post Type'=>'Filter by Post Type','Search...'=>'Search…','Select taxonomy'=>'Select taxonomy','Select post type'=>'Select post type','No matches found'=>'No matches found','Loading'=>'Loading','Maximum values reached ( {max} values )'=>'Maximum values reached ( {max} values )','Relationship'=>'Relationship','Comma separated list. Leave blank for all types'=>'Comma separated list. Leave blank for all types','Maximum'=>'Maximum','File size'=>'File size','Restrict which images can be uploaded'=>'Restrict which images can be uploaded','Minimum'=>'Minimum','Uploaded to post'=>'Uploaded to post','All'=>'All','Limit the media library choice'=>'Limit the media library choice','Library'=>'Library','Preview Size'=>'Preview Size','Image ID'=>'Image ID','Image URL'=>'Image URL','Image Array'=>'Image Array','Specify the returned value on front end'=>'Specify the returned value on front end','Return Value'=>'Return Value','Add Image'=>'Add Image','No image selected'=>'No image selected','Remove'=>'Remove','Edit'=>'Edit','All images'=>'All images','Update Image'=>'Update Image','Edit Image'=>'Edit Image','Select Image'=>'Select Image','Image'=>'Image','Allow HTML markup to display as visible text instead of rendering'=>'Allow HTML markup to display as visible text instead of rendering','Escape HTML'=>'Escape HTML','No Formatting'=>'No Formatting','Automatically add &lt;br&gt;'=>'Automatically add &lt;br&gt;','Automatically add paragraphs'=>'Automatically add paragraphs','Controls how new lines are rendered'=>'Controls how new lines are rendered','New Lines'=>'New Lines','Week Starts On'=>'Week Starts On','The format used when saving a value'=>'The format used when saving a value','Save Format'=>'Save Format','Date Picker JS weekHeaderWk'=>'Wk','Date Picker JS prevTextPrev'=>'Prev','Date Picker JS nextTextNext'=>'Next','Date Picker JS currentTextToday'=>'Today','Date Picker JS closeTextDone'=>'Done','Date Picker'=>'Date Picker','Width'=>'Width','Embed Size'=>'Embed Size','Enter URL'=>'Enter URL','oEmbed'=>'oEmbed','Text shown when inactive'=>'Text shown when inactive','Off Text'=>'Off Text','Text shown when active'=>'Text shown when active','On Text'=>'On Text','Default Value'=>'Default Value','Displays text alongside the checkbox'=>'Displays text alongside the checkbox','Message'=>'Message','No'=>'No','Yes'=>'Yes','True / False'=>'True / False','Row'=>'Row','Table'=>'Table','Block'=>'Block','Specify the style used to render the selected fields'=>'Specify the style used to render the selected fields','Layout'=>'Layout','Sub Fields'=>'Sub Fields','Group'=>'Group','Customize the map height'=>'Customize the map height','Height'=>'Height','Set the initial zoom level'=>'Set the initial zoom level','Zoom'=>'Zoom','Center the initial map'=>'Centre the initial map','Center'=>'Centre','Search for address...'=>'Search for address…','Find current location'=>'Find current location','Clear location'=>'Clear location','Search'=>'Search','Sorry, this browser does not support geolocation'=>'Sorry, this browser does not support geolocation','Google Map'=>'Google Map','The format returned via template functions'=>'The format returned via template functions','Return Format'=>'Return Format','Custom:'=>'Custom:','The format displayed when editing a post'=>'The format displayed when editing a post','Display Format'=>'Display Format','Time Picker'=>'Time Picker','No Fields found in Trash'=>'No Fields found in Trash','No Fields found'=>'No Fields found','Search Fields'=>'Search Fields','View Field'=>'View Field','New Field'=>'New Field','Edit Field'=>'Edit Field','Add New Field'=>'Add New Field','Field'=>'Field','Fields'=>'Fields','No Field Groups found in Trash'=>'No Field Groups found in Trash','No Field Groups found'=>'No Field Groups found','Search Field Groups'=>'Search Field Groups','View Field Group'=>'View Field Group','New Field Group'=>'New Field Group','Edit Field Group'=>'Edit Field Group','Add New Field Group'=>'Add New Field Group','Add New'=>'Add New','Field Group'=>'Field Group','Field Groups'=>'Field Groups','Customize WordPress with powerful, professional and intuitive fields.'=>'Customize WordPress with powerful, professional and intuitive fields.','https://www.advancedcustomfields.com'=>'https://www.advancedcustomfields.com','Advanced Custom Fields'=>'Advanced Custom Fields']];