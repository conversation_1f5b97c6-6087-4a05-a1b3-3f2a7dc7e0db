(()=>{var e={237:()=>{var e;e=jQuery,new acf.Model({id:"internalPostTypeSettingsManager",wait:"ready",events:{"blur .acf_slugify_to_key":"onChangeSlugify","blur .acf_singular_label":"onChangeSingularLabel","blur .acf_plural_label":"onChangePluralLabel","change .acf_hierarchical_switch":"onChangeHierarchical","click .acf-regenerate-labels":"onClickRegenerateLabels","click .acf-clear-labels":"onClickClearLabels","change .rewrite_slug_field":"onChangeURLSlug","keyup .rewrite_slug_field":"onChangeURLSlug"},onChangeSlugify:function(t,a){const n=a.val(),l=e(".acf_slugified_key");if(""==l.val().trim()){let e=acf.strSanitize(n.trim()).replaceAll("_","-");e=acf.applyFilters("generate_internal_post_type_name",e,this);let t=0;"taxonomy"===acf.get("screen")?t=32:"post_type"===acf.get("screen")&&(t=20),t&&(e=e.substring(0,t)),l.val(e)}},initialize:function(){if(!["taxonomy","post_type"].includes(acf.get("screen")))return;const t=function(t){if(void 0===t.element)return t;const a=e(t.element.parentElement),n=e('<span class="acf-selection"></span>');n.html(acf.strEscape(t.element.innerHTML));let l=!1;return a.filter(".acf-taxonomy-manage_terms, .acf-taxonomy-edit_terms, .acf-taxonomy-delete_terms").length&&"manage_categories"===t.id||a.filter(".acf-taxonomy-assign_terms").length&&"edit_posts"===t.id?l=!0:"taxonomy_key"!==t.id&&"post_type_key"!==t.id&&"default"!==t.id||(l=!0),l&&n.append('<span class="acf-select2-default-pill">'+acf.__("Default")+"</span>"),n.data("element",t.element),n};acf.newSelect2(e("select.query_var"),{field:!1,templateSelection:t,templateResult:t}),acf.newSelect2(e("select.acf-taxonomy-manage_terms"),{field:!1,templateSelection:t,templateResult:t}),acf.newSelect2(e("select.acf-taxonomy-edit_terms"),{field:!1,templateSelection:t,templateResult:t}),acf.newSelect2(e("select.acf-taxonomy-delete_terms"),{field:!1,templateSelection:t,templateResult:t}),acf.newSelect2(e("select.acf-taxonomy-assign_terms"),{field:!1,templateSelection:t,templateResult:t}),acf.newSelect2(e("select.meta_box"),{field:!1,templateSelection:t,templateResult:t});const a=acf.newSelect2(e("select.permalink_rewrite"),{field:!1,templateSelection:t,templateResult:t});e(".rewrite_slug_field").trigger("change"),a.on("change",function(t){e(".rewrite_slug_field").trigger("change")})},onChangeURLSlug:function(t,a){const n=e("div.acf-field.acf-field-permalink-rewrite"),l=n.find("select").find("option:selected").val(),i=n.data(l+"_instructions"),c=n.data("site_url"),s=n.find("p.description").first();if("taxonomy_key"===l||"post_type_key"===l)var o=e(".acf_slugified_key").val().trim();else o=a.val().trim();o.length||(o="{slug}"),s.html(e("<span>"+i+"</span>").text().replace("{slug}","<strong>"+e("<span>"+c+"/"+o+"</span>").text()+"</strong>"))},onChangeSingularLabel:function(e,t){const a=t.val();this.updateLabels(a,"singular",!1)},onChangePluralLabel:function(e,t){const a=t.val();this.updateLabels(a,"plural",!1)},onChangeHierarchical:function(t,a){const n=a.is(":checked");if("taxonomy"===acf.get("screen")){let t=e(".acf-field-meta-box").data("tags_meta_box");n&&(t=e(".acf-field-meta-box").data("categories_meta_box")),e("#acf_taxonomy-meta_box").find("option:first").text(t).trigger("change")}this.updatePlaceholders(n)},onClickRegenerateLabels:function(t,a){this.updateLabels(e(".acf_singular_label").val(),"singular",!0),this.updateLabels(e(".acf_plural_label").val(),"plural",!0)},onClickClearLabels:function(e,t){this.clearLabels()},updateLabels(t,a,n){e('[data-label][data-replace="'+a+'"').each((a,l)=>{var i=e(l).find('input[type="text"]').first();(n||""==i.val())&&""!=t&&i.val("lower"===e(l).data("transform")?e(l).data("label").replace("%s",t.toLowerCase()):e(l).data("label").replace("%s",t))})},clearLabels(){e("[data-label]").each((t,a)=>{e(a).find('input[type="text"]').first().val("")})},updatePlaceholders(t){if("post_type"==acf.get("screen")){var a=acf.__("Post"),n=acf.__("Posts");t&&(a=acf.__("Page"),n=acf.__("Pages"))}else a=acf.__("Tag"),n=acf.__("Tags"),t&&(a=acf.__("Category"),n=acf.__("Categories"));e("[data-label]").each((t,l)=>{var i="plural"===e(l).data("replace")?n:a;"lower"===e(l).data("transform")&&(i=i.toLowerCase()),e(l).find('input[type="text"]').first().attr("placeholder",e(l).data("label").replace("%s",i))})}}),new acf.Model({id:"advancedSettingsMetaboxManager",wait:"load",events:{"change .acf-advanced-settings-toggle":"onToggleACFAdvancedSettings","change #screen-options-wrap #acf-advanced-settings-hide":"onToggleScreenOptionsAdvancedSettings"},initialize:function(){this.$screenOptionsToggle=e("#screen-options-wrap #acf-advanced-settings-hide:first"),this.$ACFAdvancedToggle=e(".acf-advanced-settings-toggle:first"),this.render()},isACFAdvancedSettingsChecked:function(){return!!this.$ACFAdvancedToggle.length&&this.$ACFAdvancedToggle.prop("checked")},isScreenOptionsAdvancedSettingsChecked:function(){return!!this.$screenOptionsToggle.length&&this.$screenOptionsToggle.prop("checked")},onToggleScreenOptionsAdvancedSettings:function(){this.isScreenOptionsAdvancedSettingsChecked()?this.isACFAdvancedSettingsChecked()||this.$ACFAdvancedToggle.trigger("click"):this.isACFAdvancedSettingsChecked()&&this.$ACFAdvancedToggle.trigger("click")},onToggleACFAdvancedSettings:function(){this.isACFAdvancedSettingsChecked()?this.isScreenOptionsAdvancedSettingsChecked()||this.$screenOptionsToggle.trigger("click"):this.isScreenOptionsAdvancedSettingsChecked()&&this.$screenOptionsToggle.trigger("click")},render:function(){this.onToggleACFAdvancedSettings()}}),new acf.Model({id:"linkFieldGroupsManager",events:{"click .acf-link-field-groups":"linkFieldGroups"},linkFieldGroups:function(){let t=!1;const a=function(a){a.preventDefault();const l=t.$("select"),i=l.val();i.length?(acf.startButtonLoading(t.$(".button")),e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax({action:"acf/link_field_groups",field_groups:i}),type:"post",dataType:"json",success:n})):l.focus()},n=function(e){t.content(e.data.content),wp.a11y&&wp.a11y.speak&&acf.__&&wp.a11y.speak(acf.__("Field groups linked successfully."),"polite"),t.$("button.acf-close-popup").focus()};e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax({action:"acf/link_field_groups"}),type:"post",dataType:"json",success:function(e){t=acf.newPopup({title:e.data.title,content:e.data.content,width:"600px"}),t.$el.addClass("acf-link-field-groups-popup"),t.on("submit","form",a)}})}})}},t={};function a(n){var l=t[n];if(void 0!==l)return l.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,a),i.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";a(237)})()})();