import{t as g}from"./toString.1e64e8a6.js";function b(r,n,u){var e=-1,o=r.length;n<0&&(n=-n>o?0:o+n),u=u>o?o:u,u<0&&(u+=o),o=n>u?0:u-n>>>0,n>>>=0;for(var s=Array(o);++e<o;)s[e]=r[e+n];return s}function R(r,n,u){var e=r.length;return u=u===void 0?e:u,!n&&u>=e?r:b(r,n,u)}var v="\\ud800-\\udfff",y="\\u0300-\\u036f",A="\\ufe20-\\ufe2f",_="\\u20d0-\\u20ff",C=y+A+_,h="\\ufe0e\\ufe0f",S="\\u200d",x=RegExp("["+S+v+C+h+"]");function t(r){return x.test(r)}function $(r){return r.split("")}var i="\\ud800-\\udfff",M="\\u0300-\\u036f",U="\\ufe20-\\ufe2f",k="\\u20d0-\\u20ff",j=M+U+k,F="\\ufe0e\\ufe0f",H="["+i+"]",a="["+j+"]",f="\\ud83c[\\udffb-\\udfff]",J="(?:"+a+"|"+f+")",c="[^"+i+"]",l="(?:\\ud83c[\\udde6-\\uddff]){2}",p="[\\ud800-\\udbff][\\udc00-\\udfff]",O="\\u200d",d=J+"?",m="["+F+"]?",T="(?:"+O+"(?:"+[c,l,p].join("|")+")"+m+d+")*",V=m+d+T,E="(?:"+[c+a+"?",a,l,p,H].join("|")+")",L=RegExp(f+"(?="+f+")|"+E+V,"g");function P(r){return r.match(L)||[]}function W(r){return t(r)?P(r):$(r)}function Z(r){return function(n){n=g(n);var u=t(n)?W(n):void 0,e=u?u[0]:n.charAt(0),o=u?R(u,1).join(""):n.slice(1);return e[r]()+o}}var w=Z("toUpperCase");export{w as u};
