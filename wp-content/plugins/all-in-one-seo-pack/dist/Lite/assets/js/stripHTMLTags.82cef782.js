import{t as h}from"./toNumber.89dbadc6.js";import{g as b}from"./default-i18n.20001971.js";import{m as k}from"./_baseSet.ca702273.js";import{a as R}from"./_arrayEach.6af5abac.js";import{s as E,i as x,t as y,a as _}from"./helpers.a0b389be.js";import{k as v}from"./_getTag.69d3a807.js";var d=1/0,T=17976931348623157e292;function W(e){if(!e)return e===0?e:0;if(e=h(e),e===d||e===-d){var r=e<0?-1:1;return r*T}return e===e?e:0}function w(e,r){return e&&E(e,r,v)}function $(e,r){return function(n,s){if(n==null)return n;if(!x(n))return e(n,s);for(var t=n.length,c=-1,a=Object(n);++c<t&&s(a[c],c,a)!==!1;);return n}}var j=$(w);function A(e){return typeof e=="function"?e:y}function F(e,r){var n=_(e)?R:j;return n(e,A(r))}function S(e){return e=e.replace(/\s{2,}/g," "),e=e.replace(/\s\./g,"."),e.trim()}var g=function(e,r){var n;for(n=0;n<e.length;n++)if(e[n].regex.test(r))return e[n]},p=function(e,r){var n,s,t;for(n=0;n<r.length;n++)if(s=g(e,r.substring(0,n+1)),s)t=s;else if(t)return{max_index:n,rule:t};return t?{max_index:r.length,rule:t}:void 0},z=function(e){var r="",n=[],s=1,t=1,c=function(a,o){e({type:o,src:a,line:s,col:t});var i=a.split(`
`);s+=i.length-1,t=(i.length>1?1:t)+i[i.length-1].length};return{addRule:function(a,o){n.push({regex:a,type:o})},onText:function(a){for(var o=r+a,i=p(n,o);i&&i.max_index!==o.length;)c(o.substring(0,i.max_index),i.rule.type),o=o.substring(i.max_index),i=p(n,o);r=o},end:function(){if(r.length!==0){var a=g(n,r);if(!a){var o=new Error("unable to tokenize");throw o.tokenizer2={buffer:r,line:s,col:t},o}c(r,a.type)}}}};const B=b(z),f=["address","article","aside","blockquote","canvas","details","dialog","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","section","table","tfoot","ul","video"],m=["b","big","i","small","tt","abbr","acronym","cite","code","dfn","em","kbd","strong","samp","time","var","a","bdo","br","img","map","object","q","script","span","sub","sup","button","input","label","select","textarea"],I=new RegExp("^<("+f.join("|")+")[^>]*?>$","i"),N=new RegExp("^</("+f.join("|")+")[^>]*?>$","i"),L=new RegExp("^<("+m.join("|")+")[^>]*>$","i"),M=new RegExp("^</("+m.join("|")+")[^>]*>$","i"),q=/^<([^>\s/]+)[^>]*>$/,C=/^<\/([^>\s]+)[^>]*>$/,G=/^[^<]+$/,O=/^<[^><]*$/,D=/<!--(.|[\r\n])*?-->/g;let u=[],l;function P(){u=[],l=B(function(e){u.push(e)}),l.addRule(G,"content"),l.addRule(O,"greater-than-sign-content"),l.addRule(I,"block-start"),l.addRule(N,"block-end"),l.addRule(L,"inline-start"),l.addRule(M,"inline-end"),l.addRule(q,"other-element-start"),l.addRule(C,"other-element-end")}function U(e){const r=[];let n=0,s="",t="",c="";return e=e.replace(D,""),P(),l.onText(e),l.end(),F(u,function(a,o){const i=u[o+1];switch(a.type){case"content":case"greater-than-sign-content":case"inline-start":case"inline-end":case"other-tag":case"other-element-start":case"other-element-end":case"greater than sign":!i||n===0&&(i.type==="block-start"||i.type==="block-end")?(t+=a.src,r.push(t),s="",t="",c=""):t+=a.src;break;case"block-start":n!==0&&(t.trim()!==""&&r.push(t),t="",c=""),n++,s=a.src;break;case"block-end":n--,c=a.src,s!==""&&c!==""?r.push(s+t+c):t.trim()!==""&&r.push(t),s="",t="",c="";break}0>n&&(n=0)}),r}const Z=k(U),X=new RegExp("</?("+f.join("|")+")[^>]*?>","ig"),ee=function(e){return e=e.replace(/<header[^>]*? class\s*=\s*["']?aioseo-toc-header[^>]+>([\S\s]*?)<\/header>/gms,""),e=e.replace(/<span[^>]*? class\s*=\s*["']?aioseo-tooltip[^>]+>([\S\s]*?)<\/span>/gms,""),e=e.replace(X," "),e=e.replace(/(<([^>]+)>)/ig,""),e=S(e),e};export{S as a,j as b,B as c,F as f,Z as m,ee as s,W as t};
