import{h as o}from"./helpers.a0b389be.js";import{i}from"./toString.1e64e8a6.js";var s=/\s/;function c(r){for(var n=r.length;n--&&s.test(r.charAt(n)););return n}var a=/^\s+/;function f(r){return r&&r.slice(0,c(r)+1).replace(a,"")}var e=NaN,p=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,m=/^0o[0-7]+$/i,y=parseInt;function x(r){if(typeof r=="number")return r;if(i(r))return e;if(o(r)){var n=typeof r.valueOf=="function"?r.valueOf():r;r=o(n)?n+"":n}if(typeof r!="string")return r===0?r:+r;r=f(r);var t=u.test(r);return t||m.test(r)?y(r.slice(2),t?2:8):p.test(r)?e:+r}export{x as t};
