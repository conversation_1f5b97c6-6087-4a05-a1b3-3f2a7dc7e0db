.aioseo-limit-modified-date-wpbakery{height:100%;position:relative}[dir=ltr] .aioseo-limit-modified-date-wpbakery{margin-right:10px}[dir=rtl] .aioseo-limit-modified-date-wpbakery{margin-left:10px}.aioseo-limit-modified-date-wpbakery__toggle{height:100%;cursor:pointer;background:#fff;transition:background-color .2s ease-in-out;display:flex;justify-content:center;align-items:center;position:relative}[dir=ltr] .aioseo-limit-modified-date-wpbakery__toggle{border-top-right-radius:5px;border-bottom-right-radius:5px}[dir=rtl] .aioseo-limit-modified-date-wpbakery__toggle{border-top-left-radius:5px;border-bottom-left-radius:5px}.aioseo-limit-modified-date-wpbakery__toggle .aioseo-caret{width:18px;height:18px;transition:transform .3s;margin:0 5px}[dir=ltr] .aioseo-limit-modified-date-wpbakery__toggle .aioseo-caret.rotated{transform:rotate(-180deg)}[dir=rtl] .aioseo-limit-modified-date-wpbakery__toggle .aioseo-caret.rotated{transform:rotate(180deg)}.aioseo-limit-modified-date-wpbakery__toggle:before{content:"";width:1px;height:80%;background-color:#34434a;opacity:.2}.aioseo-limit-modified-date-wpbakery__toggle:hover{background-color:#e9f2f6}.vc_navbar-frontend:not(.vc_post-custom-layout-selected) .aioseo-limit-modified-date-wpbakery__toggle{opacity:.2;pointer-events:none}.aioseo-limit-modified-date-wpbakery__options{position:absolute;top:100%;width:200px;display:flex;justify-content:end}[dir=ltr] .aioseo-limit-modified-date-wpbakery__options{right:0}[dir=rtl] .aioseo-limit-modified-date-wpbakery__options{left:0}.aioseo-limit-modified-date-wpbakery__option{padding:15px;line-height:1;color:#fff;cursor:pointer;transition:background-color .2s ease-in-out;background-color:#00447f}.aioseo-limit-modified-date-wpbakery__option:hover{background-color:#0772ce}.aioseo-wpbakery-integration{padding:12px 30px}.aioseo-wpbakery-integration>.aioseo-score-button{background:#fff;cursor:pointer}.aioseo-wpbakery-integration .aioseo-gear-icon{width:28px;height:28px;color:#fff;margin:2px 0;cursor:pointer}
