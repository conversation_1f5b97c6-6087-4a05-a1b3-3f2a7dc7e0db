<?php
namespace AIOSEO\Plugin\Common\Options;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

use AIOSEO\Plugin\Common\Traits;

/**
 * Class that holds all internal options for AIOSEO.
 *
 * @since 4.0.0
 */
class InternalOptions {
	use Traits\Options;

	/**
	 * Holds a list of all the possible deprecated options.
	 *
	 * @since 4.0.0
	 *
	 * @var array
	 */
	protected $allDeprecatedOptions = [
		'autogenerateDescriptions',
		'breadcrumbsEnable',
		'descriptionFormat',
		'enableSchemaMarkup',
		'excludePosts',
		'excludeTerms',
		'googleAnalytics',
		'noPaginationForCanonical',
		'staticSitemap',
		'staticVideoSitemap',
		'useContentForAutogeneratedDescriptions'
	];

	/**
	 * All the default options.
	 *
	 * @since 4.0.0
	 *
	 * @var array
	 */
	protected $defaults = [
		// phpcs:disable WordPress.Arrays.ArrayDeclarationSpacing.AssociativeArrayFound
		'internal'     => [
			'connectLicenseKey' => [ 'type' => 'string' ],
			'lastActiveVersion' => [ 'type' => 'string', 'default' => '0.0' ],
			'migratedVersion'   => [ 'type' => 'string' ],
			'siteAnalysis'      => [
				'connectToken' => [ 'type' => 'string' ],
			],
			'headlineAnalysis'  => [
				'headlines' => [ 'type' => 'array', 'default' => [] ]
			],
			'wizard'            => [ 'type' => 'string' ],
			'category'          => [ 'type' => 'string' ],
			'categoryOther'     => [ 'type' => 'string' ],
			'deprecatedOptions' => [ 'type' => 'array', 'default' => [] ],
			'searchStatistics'  => [
				'profile'    => [ 'type' => 'array', 'default' => [] ],
				'trustToken' => [ 'type' => 'string' ],
				'rolling'    => [ 'type' => 'string', 'default' => 'last28Days' ],
				'site'       => [
					'verified'  => [ 'type' => 'boolean', 'default' => false ],
					'lastFetch' => [ 'type' => 'number', 'default' => 0 ]
				],
				'sitemap'    => [
					'list'      => [ 'type' => 'array', 'default' => [] ],
					'ignored'   => [ 'type' => 'array', 'default' => [] ],
					'lastFetch' => [ 'type' => 'number', 'default' => 0 ]
				]
			],
			'ai'                => [
				'accessToken'        => [ 'type' => 'string', 'default' => '' ],
				'isTrialAccessToken' => [ 'type' => 'boolean', 'default' => false ],
				'credits'            => [
					'total'     => [ 'type' => 'number', 'default' => 0 ],
					'remaining' => [ 'type' => 'number', 'default' => 0 ],
					'orders'    => [ 'type' => 'array', 'default' => [] ],
					'license'   => [
						'total'     => [ 'type' => 'number', 'default' => 0 ],
						'remaining' => [ 'type' => 'number', 'default' => 0 ],
						'expires'   => [ 'type' => 'number', 'default' => 0 ]
					]
				]
			]
		],
		'integrations' => [
			'semrush' => [
				'accessToken'  => [ 'type' => 'string' ],
				'tokenType'    => [ 'type' => 'string' ],
				'expires'      => [ 'type' => 'string' ],
				'refreshToken' => [ 'type' => 'string' ]
			]
		],
		'database'     => [
			'installedTables' => [ 'type' => 'string' ]
		]
		// phpcs:enable WordPress.Arrays.ArrayDeclarationSpacing.AssociativeArrayFound
	];

	/**
	 * The Construct method.
	 *
	 * @since 4.0.0
	 *
	 * @param string $optionsName The options name.
	 */
	public function __construct( $optionsName = 'aioseo_options_internal' ) {
		$this->optionsName = $optionsName;

		$this->init();

		add_action( 'shutdown', [ $this, 'save' ] );
	}

	/**
	 * Initializes the options.
	 *
	 * @since 4.0.0
	 *
	 * @return void
	 */
	protected function init() {
		// Options from the DB.
		$dbOptions = $this->getDbOptions( $this->optionsName );

		// Refactor options.
		$this->defaultsMerged = array_replace_recursive( $this->defaults, $this->defaultsMerged );

		$options = array_replace_recursive(
			$this->defaultsMerged,
			$this->addValueToValuesArray( $this->defaultsMerged, $dbOptions )
		);

		aioseo()->core->optionsCache->setOptions( $this->optionsName, apply_filters( 'aioseo_get_options_internal', $options ) );

		// Get the localized options.
		$dbOptionsLocalized = get_option( $this->optionsName . '_localized' );
		if ( empty( $dbOptionsLocalized ) ) {
			$dbOptionsLocalized = [];
		}
		$this->localized = $dbOptionsLocalized;
	}

	/**
	 * Get all the deprecated options.
	 *
	 * @since 4.0.0
	 *
	 * @param  bool  $includeNamesAndValues Whether or not to include option names.
	 * @return array                        An array of deprecated options.
	 */
	public function getAllDeprecatedOptions( $includeNamesAndValues = false ) {
		if ( ! $includeNamesAndValues ) {
			return $this->allDeprecatedOptions;
		}

		$options = [];
		foreach ( $this->allDeprecatedOptions as $deprecatedOption ) {
			$options[] = [
				'label'   => ucwords( str_replace( '_', ' ', aioseo()->helpers->toSnakeCase( $deprecatedOption ) ) ),
				'value'   => $deprecatedOption,
				'enabled' => in_array( $deprecatedOption, aioseo()->internalOptions->internal->deprecatedOptions, true )
			];
		}

		return $options;
	}

	/**
	 * Sanitizes, then saves the options to the database.
	 *
	 * @since 4.0.0
	 *
	 * @param  array $options An array of options to sanitize, then save.
	 * @return void
	 */
	public function sanitizeAndSave( $options ) {
		if ( ! is_array( $options ) ) {
			return;
		}

		// First, recursively replace the new options into the cached state.
		// It's important we use the helper method since we want to replace populated arrays with empty ones if needed (when a setting was cleared out).
		$cachedOptions = aioseo()->core->optionsCache->getOptions( $this->optionsName );
		$dbOptions     = aioseo()->helpers->arrayReplaceRecursive(
			$cachedOptions,
			$this->addValueToValuesArray( $cachedOptions, $options, [], true )
		);

		// Now, we must also intersect both arrays to delete any individual keys that were unset.
		// We must do this because, while arrayReplaceRecursive will update the values for keys or empty them out,
		// it will keys that aren't present in the replacement array unaffected in the target array.
		$dbOptions = aioseo()->helpers->arrayIntersectRecursive(
			$dbOptions,
			$this->addValueToValuesArray( $cachedOptions, $options, [], true ),
			'value'
		);

		// Update the cache state.
		aioseo()->core->optionsCache->setOptions( $this->optionsName, $dbOptions );

		// Update localized options.
		update_option( $this->optionsName . '_localized', $this->localized );

		// Finally, save the new values to the DB.
		$this->save( true );
	}
}