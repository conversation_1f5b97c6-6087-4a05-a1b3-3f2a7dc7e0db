<?php
namespace AIOSEO\Plugin\Common\Integrations;

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Route class for the API.
 *
 * @since 4.3.8
 */
class WpCode {
	/**
	 * Load the WPCode snippets for our desired username or return an empty array if not available.
	 *
	 * @since 4.3.8
	 *
	 * @return array The snippets.
	 */
	public static function loadWpCodeSnippets() {
		$snippets = self::getPlaceholderSnippets();
		if ( function_exists( 'wpcode_get_library_snippets_by_username' ) ) {
			$snippets = wpcode_get_library_snippets_by_username( 'aioseo' );
		}

		return $snippets;
	}

	/**
	 * Checks if the plugin is installed, either the lite or premium version.
	 *
	 * @since 4.3.8
	 *
	 * @return bool True if the plugin is installed.
	 */
	public static function isPluginInstalled() {
		return self::isProInstalled() || self::isLiteInstalled();
	}

	/**
	 * Is the pro plugin installed.
	 *
	 * @since 4.3.8
	 *
	 * @return bool True if the pro plugin is installed.
	 */
	public static function isProInstalled() {
		$installedPlugins = array_keys( get_plugins() );

		return in_array( 'wpcode-premium/wpcode.php', $installedPlugins, true );
	}

	/**
	 * Is the lite plugin installed.
	 *
	 * @since 4.3.8
	 *
	 * @return bool True if the lite plugin is installed.
	 */
	public static function isLiteInstalled() {
		$installedPlugins = array_keys( get_plugins() );

		return in_array( 'insert-headers-and-footers/ihaf.php', $installedPlugins, true );
	}

	/**
	 * Basic check if the plugin is active by looking for the main function.
	 *
	 * @since 4.3.8
	 *
	 * @return bool True if the plugin is active.
	 */
	public static function isPluginActive() {
		return function_exists( 'wpcode' );
	}

	/**
	 * Checks if the plugin is active but needs to be updated by checking if the function to load the
	 * library snippets by username exists.
	 *
	 * @since 4.3.8
	 *
	 * @return bool True if the plugin is active but needs to be updated.
	 */
	public static function pluginNeedsUpdate() {
		return self::isPluginActive() && ! function_exists( 'wpcode_get_library_snippets_by_username' );
	}

	/**
	 * Get placeholder snippets if the WPCode snippets are not available.
	 *
	 * @since 4.3.8
	 *
	 * @return array The placeholder snippets.
	 */
	private static function getPlaceholderSnippets() {
		$snippetTitles = [
			'Disable autogenerated shipping details schema for WooCommerce',
			'Disable SEO Preview feature',
			'Disable Shortcode Parsing in All in One SEO',
			'Enable WooCommerce Product Attributes in Search Appearance',
			'Fix LearnPress conflict that hides AIOSEO tabs on settings pages',
			'Limit Meta Description to 160 characters',
			'Limit SEO Title to 60 characters',
			'Noindex Product Search Pages',
			'Noindex Products under a Product Category',
		];

		$placeholderSnippets = [];
		foreach ( $snippetTitles as $snippetTitle ) {
			// Add placeholder install link so we show a button.
			$placeholderSnippets[] = [
				'title'   => $snippetTitle,
				'install' => 'https://library.wpcode.com/'
			];
		}

		return $placeholderSnippets;
	}
}