[12-Aug-2025 07:59:10 UTC] PHP Warning:  Constant REQUESTS_SILENCE_PSR0_DEPRECATIONS already defined in /var/www/html/event-booking.local/wp-content/themes/cosmictreefoundation/vendor/razorpay/razorpay/Deprecated.php on line 14
[12-Aug-2025 07:59:10 UTC] Events post type registered
[12-Aug-2025 07:59:10 UTC] Order Details post type registered
[12-Aug-2025 07:59:10 UTC] WordPress database error You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'FOREIGN KEY (video_id) REFERENCES wp_video_booking_videos(id) ON DELETE CASCADE' at line 1 for query ALTER TABLE wp_video_booking_order_items ADD COLUMN FOREIGN KEY (video_id) REFERENCES wp_video_booking_videos(id) ON DELETE CASCADE made by require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, VideoBookingSystem->init, VideoBookingSystem->create_tables, dbDelta
[12-Aug-2025 07:59:10 UTC] WordPress database error You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'FOREIGN KEY (video_id) REFERENCES wp_video_booking_videos(id) ON DELETE CASCADE' at line 1 for query ALTER TABLE wp_video_booking_access ADD COLUMN FOREIGN KEY (video_id) REFERENCES wp_video_booking_videos(id) ON DELETE CASCADE made by require_once('wp-config.php'), require_once('wp-settings.php'), do_action('init'), WP_Hook->do_action, WP_Hook->apply_filters, VideoBookingSystem->init, VideoBookingSystem->create_tables, dbDelta
