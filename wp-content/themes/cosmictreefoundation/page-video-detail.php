<?php
/**
 * Template Name: Video Detail
 * 
 * @package CosmicTreeFoundation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get video slug from URL parameter
$video_slug = get_query_var('video_slug');
if (empty($video_slug)) {
    $video_slug = isset($_GET['video']) ? sanitize_title($_GET['video']) : '';
}

if (empty($video_slug)) {
    // Redirect to workshop recordings if no video specified
    wp_redirect(home_url('/workshop-recordings'));
    exit;
}

// Get video by slug
$video = VideoBookingDB::get_video($video_slug, 'slug');
if (!$video) {
    // Video not found, show 404
    global $wp_query;
    $wp_query->set_404();
    status_header(404);
    get_template_part('404');
    exit;
}

// Set global video variable for the template
global $current_video;
$current_video = $video;

// Include the video booking detail template
if (file_exists(get_template_directory() . '/video-booking/templates/detail.php')) {
    include get_template_directory() . '/video-booking/templates/detail.php';
} else {
    // Fallback if template doesn't exist
    get_header();
    ?>
    <div class="container">
        <h1>Video Detail</h1>
        <p>Video booking system is not properly configured.</p>
    </div>
    <?php
    get_footer();
}
