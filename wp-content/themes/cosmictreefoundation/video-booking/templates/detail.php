<?php

/**
 * Video Detail Template
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get video slug from URL
$video_slug = get_query_var('video_slug');
if (!$video_slug) {
    wp_redirect(home_url('/workshop-recordings'));
    exit;
}

// Get video by slug
$video = VideoBookingDB::get_video($video_slug, 'slug');
if (!$video || !$video->is_available) {
    wp_redirect(home_url('/workshop-recordings'));
    exit;
}

get_header();

// Check if early bird pricing is still active
$is_early_bird_expired = false;
if ($video->is_early_bird && $video->early_bird_end_date) {
    $is_early_bird_expired = strtotime($video->early_bird_end_date) <= current_time('timestamp');
}

$is_early_bird_active = $video->is_early_bird && !$is_early_bird_expired && $video->upload_status === 'pending';
$current_price = $is_early_bird_active ? $video->early_bird_price : $video->regular_price;
$user_has_access = false;

// Check if user has access (if logged in)
if (is_user_logged_in()) {
    $user_has_access = VideoBookingDB::has_video_access(get_current_user_id(), $video->id);
}
?>

<div class="video-booking-container">
    <div class="video-detail-container">
        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <a href="<?php echo home_url('/workshop-recordings'); ?>">Workshop Recordings</a>
            <span class="separator">></span>
            <span class="current"><?php echo esc_html($video->title); ?></span>
        </nav>

        <div class="video-detail-content">
            <div class="video-main">
                <div class="video-thumbnail-large">
                    <?php if ($video->thumbnail): ?>
                        <img src="<?php echo esc_url($video->thumbnail); ?>" alt="<?php echo esc_attr($video->title); ?>">
                    <?php else: ?>
                        <div class="no-thumbnail-large">
                            <i class="fa fa-play-circle"></i>
                            <span>Video Preview</span>
                        </div>
                    <?php endif; ?>

                    <?php if ($is_early_bird_active): ?>
                        <div class="early-bird-badge-large">Early Bird Offer</div>
                    <?php endif; ?>

                    <?php if ($user_has_access): ?>
                        <div class="access-badge">
                            <i class="fa fa-check-circle"></i>
                            You have access to this video
                        </div>
                    <?php endif; ?>
                </div>

                <div class="video-info-detailed">
                    <h1 class="video-title-large"><?php echo esc_html($video->title); ?></h1>

                    <?php if ($video->description): ?>
                        <div class="video-description-full">
                            <?php echo wp_kses_post(wpautop($video->description)); ?>
                        </div>
                    <?php endif; ?>

                    <div class="video-features">
                        <div class="feature">
                            <i class="fa fa-clock-o"></i>
                            <span><strong><?php echo $video->duration_days; ?> days</strong> access period</span>
                        </div>
                        <div class="feature">
                            <i class="fa fa-mobile"></i>
                            <span>Watch on any device</span>
                        </div>
                        <div class="feature">
                            <?php if ($is_early_bird_active): ?>
                                <i class="fa fa-calendar"></i>
                                <span>Available after workshop completion</span>
                            <?php else: ?>
                                <i class="fa fa-bolt"></i>
                                <span>Instant access after purchase</span>
                            <?php endif; ?>
                        </div>
                        <div class="feature">
                            <i class="fa fa-hd-video"></i>
                            <span>High-quality video streaming</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="video-sidebar">
                <div class="purchase-card">
                    <div class="price-section">
                        <div class="current-price">₹<?php echo number_format($current_price, 2); ?></div>

                        <?php if ($video->is_early_bird && $video->regular_price > $video->early_bird_price): ?>
                            <div class="original-price">₹<?php echo number_format($video->regular_price, 2); ?></div>
                            <div class="discount-badge">
                                Save ₹<?php echo number_format($video->regular_price - $video->early_bird_price, 2); ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if ($is_early_bird_active && $video->early_bird_note): ?>
                        <div class="early-bird-info">
                            <i class="fa fa-info-circle"></i>
                            <p><?php echo esc_html($video->early_bird_note); ?></p>
                        </div>
                    <?php endif; ?>

                    <?php if ($user_has_access): ?>
                        <div class="access-actions">
                            <a href="<?php echo home_url('/my-account/my-recordings'); ?>" class="btn btn-success btn-full-width">
                                <i class="fa fa-play"></i>
                                Watch Now
                            </a>
                            <p class="access-note">Go to My Recordings to watch this video</p>
                        </div>
                    <?php else: ?>
                        <div class="purchase-actions">
                            <button class="btn btn-primary btn-full-width add-to-cart"
                                data-video-id="<?php echo $video->id; ?>"
                                data-early-bird="<?php echo $is_early_bird_active ? 1 : 0; ?>">
                                <i class="fa fa-shopping-cart"></i>
                                Add to Cart
                            </button>

                            <div class="quick-buy">
                                <button class="btn btn-success btn-full-width buy-now"
                                    data-video-id="<?php echo $video->id; ?>"
                                    data-early-bird="<?php echo $is_early_bird_active ? 1 : 0; ?>">
                                    <i class="fa fa-bolt"></i>
                                    Buy Now
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="security-badges">
                        <div class="badge">
                            <i class="fa fa-shield"></i>
                            <span>Secure Payment</span>
                        </div>
                        <div class="badge">
                            <i class="fa fa-mobile"></i>
                            <span>Mobile Friendly</span>
                        </div>
                        <div class="badge">
                            <i class="fa fa-support"></i>
                            <span>24/7 Support</span>
                        </div>
                    </div>
                </div>

                <!-- Related Videos -->
                <?php
                $related_videos = VideoBookingDB::get_videos(array(
                    'status' => 'available',
                    'limit' => 3
                ));

                // Remove current video from related
                $related_videos = array_filter($related_videos, function ($v) use ($video) {
                    return $v->id !== $video->id;
                });
                $related_videos = array_slice($related_videos, 0, 3);
                ?>

                <?php if (!empty($related_videos)): ?>
                    <div class="related-videos">
                        <h3>Other Workshop Recordings</h3>
                        <?php foreach ($related_videos as $related): ?>
                            <?php
                            $related_slug = sanitize_title($related->title);
                            $related_url = home_url('/workshop-recordings/' . $related_slug);
                            $related_price = $related->is_early_bird ? $related->early_bird_price : $related->regular_price;
                            ?>
                            <div class="related-video">
                                <div class="related-thumbnail">
                                    <?php if ($related->thumbnail): ?>
                                        <img src="<?php echo esc_url($related->thumbnail); ?>" alt="<?php echo esc_attr($related->title); ?>">
                                    <?php else: ?>
                                        <div class="no-thumb"><i class="fa fa-play"></i></div>
                                    <?php endif; ?>
                                </div>
                                <div class="related-info">
                                    <h4><a href="<?php echo esc_url($related_url); ?>"><?php echo esc_html($related->title); ?></a></h4>
                                    <div class="related-price">₹<?php echo number_format($related_price, 2); ?></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
    /* Video Detail Styles */
    .video-detail-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .breadcrumb {
        margin-bottom: 30px;
        font-size: 14px;
        color: #666;
    }

    .breadcrumb a {
        color: #2c5aa0;
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .separator {
        margin: 0 10px;
    }

    .current {
        color: #333;
        font-weight: bold;
    }

    .video-detail-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 40px;
    }

    .video-thumbnail-large {
        position: relative;
        width: 100%;
        height: 400px;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 30px;
    }

    .video-thumbnail-large img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-thumbnail-large {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        background: #f0f0f0;
        color: #666;
    }

    .no-thumbnail-large i {
        font-size: 4em;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    .early-bird-badge-large {
        position: absolute;
        top: 20px;
        right: 20px;
        background: #ff6b35;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: bold;
        font-size: 14px;
    }

    .access-badge {
        position: absolute;
        bottom: 20px;
        left: 20px;
        background: #28a745;
        color: white;
        padding: 10px 15px;
        border-radius: 6px;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .video-title-large {
        font-size: 2.2em;
        margin-bottom: 20px;
        color: #333;
        line-height: 1.3;
    }

    .video-description-full {
        font-size: 1.1em;
        line-height: 1.7;
        color: #555;
        margin-bottom: 30px;
    }

    .video-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-bottom: 30px;
    }

    .feature {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .feature i {
        color: #2c5aa0;
        font-size: 1.2em;
    }

    /* Purchase Card */
    .purchase-card {
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        position: sticky;
        top: 20px;
    }

    .price-section {
        text-align: center;
        margin-bottom: 25px;
    }

    .current-price {
        font-size: 2.5em;
        font-weight: bold;
        color: #2c5aa0;
        margin-bottom: 10px;
    }

    .original-price {
        font-size: 1.2em;
        color: #999;
        text-decoration: line-through;
        margin-bottom: 10px;
    }

    .discount-badge {
        background: #28a745;
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        display: inline-block;
    }

    .early-bird-info {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 25px;
    }

    .early-bird-info i {
        color: #856404;
        margin-right: 8px;
    }

    .early-bird-info p {
        margin: 0;
        color: #856404;
        font-size: 14px;
    }

    .purchase-actions {
        margin-bottom: 25px;
    }

    .quick-buy {
        margin-top: 15px;
    }

    .access-actions {
        margin-bottom: 25px;
        text-align: center;
    }

    .access-note {
        margin-top: 10px;
        font-size: 14px;
        color: #666;
        text-align: center;
    }

    .security-badges {
        display: flex;
        justify-content: space-around;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .security-badges .badge {
        text-align: center;
        font-size: 12px;
        color: #666;
    }

    .security-badges .badge i {
        display: block;
        font-size: 1.5em;
        margin-bottom: 5px;
        color: #2c5aa0;
    }

    /* Related Videos */
    .related-videos {
        margin-top: 30px;
    }

    .related-videos h3 {
        margin-bottom: 20px;
        color: #333;
    }

    .related-video {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .related-thumbnail {
        width: 80px;
        height: 60px;
        border-radius: 6px;
        overflow: hidden;
        flex-shrink: 0;
    }

    .related-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-thumb {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: #ddd;
        color: #999;
    }

    .related-info h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
    }

    .related-info h4 a {
        color: #333;
        text-decoration: none;
    }

    .related-info h4 a:hover {
        color: #2c5aa0;
    }

    .related-price {
        font-weight: bold;
        color: #2c5aa0;
        font-size: 14px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .video-detail-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }

        .video-thumbnail-large {
            height: 250px;
        }

        .video-title-large {
            font-size: 1.8em;
        }

        .video-features {
            grid-template-columns: 1fr;
        }

        .purchase-card {
            position: static;
        }

        .current-price {
            font-size: 2em;
        }
    }
</style>

<script>
    jQuery(document).ready(function($) {
        // Buy now functionality
        $('.buy-now').click(function(e) {
            e.preventDefault();

            const $button = $(this);
            const videoId = $button.data('video-id');
            const isEarlyBird = $button.data('early-bird');

            // Add to cart and redirect to checkout
            $.ajax({
                url: videoBooking.ajax_url,
                type: 'POST',
                data: {
                    action: 'video_add_to_cart',
                    nonce: videoBooking.nonce,
                    video_id: videoId,
                    is_early_bird: isEarlyBird
                },
                success: function(response) {
                    if (response.success) {
                        window.location.href = videoBooking.site_url + '/video-checkout';
                    } else {
                        alert(response.data || 'Failed to add video to cart');
                    }
                },
                error: function() {
                    alert('Network error. Please try again.');
                }
            });
        });
    });
</script>

<?php get_footer(); ?>