<?php

/**
 * Account Dashboard Template
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();

$current_user = wp_get_current_user();
$user_access_summary = VideoBookingAccessControl::get_user_access_summary($current_user->ID);
?>

<div class="video-booking-container">
    <div class="account-dashboard">
        <div class="dashboard-header">
            <h1>Welcome back, <?php echo esc_html($current_user->display_name); ?>!</h1>
            <p>Manage your account and access your workshop recordings</p>
        </div>

        <div class="dashboard-stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo $user_access_summary['total_videos']; ?></div>
                <div class="stat-label">Total Videos</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $user_access_summary['accessible_videos']; ?></div>
                <div class="stat-label">Available Now</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $user_access_summary['pending_videos']; ?></div>
                <div class="stat-label">Coming Soon</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $user_access_summary['expiring_soon']; ?></div>
                <div class="stat-label">Expiring Soon</div>
            </div>
        </div>

        <div class="dashboard-content">
            <div class="dashboard-main">
                <div class="dashboard-section">
                    <h2>Quick Actions</h2>
                    <div class="action-grid">
                        <a href="<?php echo home_url('/my-account/my-recordings'); ?>" class="action-card">
                            <i class="fa fa-play-circle"></i>
                            <h3>My Recordings</h3>
                            <p>Watch your purchased workshop videos</p>
                        </a>

                        <a href="<?php echo home_url('/workshop-recordings'); ?>" class="action-card">
                            <i class="fa fa-shopping-cart"></i>
                            <h3>Browse Videos</h3>
                            <p>Discover new workshop recordings</p>
                        </a>

                        <a href="<?php echo home_url('/video-cart'); ?>" class="action-card">
                            <i class="fa fa-shopping-bag"></i>
                            <h3>View Cart</h3>
                            <p>Complete your pending purchases</p>
                        </a>

                        <a href="#" class="action-card" onclick="showProfileEdit()">
                            <i class="fa fa-user"></i>
                            <h3>Edit Profile</h3>
                            <p>Update your account information</p>
                        </a>
                    </div>
                </div>

                <div class="dashboard-section">
                    <h2>Recent Activity</h2>
                    <div class="activity-list">
                        <?php
                        // Get recent orders
                        global $wpdb;
                        $orders_table = $wpdb->prefix . 'video_booking_orders';
                        $recent_orders = $wpdb->get_results($wpdb->prepare(
                            "SELECT * FROM $orders_table WHERE user_id = %d ORDER BY created_at DESC LIMIT 5",
                            $current_user->ID
                        ));

                        if ($recent_orders): ?>
                            <?php foreach ($recent_orders as $order): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fa fa-shopping-cart"></i>
                                    </div>
                                    <div class="activity-content">
                                        <h4>Order #<?php echo esc_html($order->order_number); ?></h4>
                                        <p>₹<?php echo number_format($order->total_amount, 2); ?> - <?php echo ucfirst($order->payment_status); ?></p>
                                        <small><?php echo date('M j, Y g:i A', strtotime($order->created_at)); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="no-activity">
                                <p>No recent activity. <a href="<?php echo home_url('/workshop-recordings'); ?>">Browse our workshop recordings</a> to get started!</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="dashboard-sidebar">
                <div class="profile-card">
                    <div class="profile-avatar">
                        <?php echo get_avatar($current_user->ID, 80); ?>
                    </div>
                    <div class="profile-info">
                        <h3><?php echo esc_html($current_user->display_name); ?></h3>
                        <p><?php echo esc_html($current_user->user_email); ?></p>
                        <small>Member since <?php echo date('M Y', strtotime($current_user->user_registered)); ?></small>
                    </div>
                </div>

                <div class="account-menu">
                    <h3>Account Menu</h3>
                    <ul>
                        <li><a href="<?php echo home_url('/my-recordings'); ?>"><i class="fa fa-play"></i> My Recordings</a></li>
                        <li><a href="<?php echo home_url('/workshop-recordings'); ?>"><i class="fa fa-video-camera"></i> Browse Videos</a></li>
                        <li><a href="#" onclick="showProfileEdit()"><i class="fa fa-user"></i> Edit Profile</a></li>
                        <li><a href="#" onclick="showPasswordChange()"><i class="fa fa-lock"></i> Change Password</a></li>
                        <li><a href="#" id="logout-link"><i class="fa fa-sign-out"></i> Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profile Edit Modal -->
<div id="profile-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Edit Profile</h2>
            <span class="modal-close">&times;</span>
        </div>
        <form id="profile-form">
            <?php wp_nonce_field('video_auth_nonce', 'nonce'); ?>
            <div class="form-group">
                <label for="display_name">Display Name</label>
                <input type="text" id="display_name" name="display_name" class="form-control" value="<?php echo esc_attr($current_user->display_name); ?>">
            </div>
            <div class="form-group">
                <label for="first_name">First Name</label>
                <input type="text" id="first_name" name="first_name" class="form-control" value="<?php echo esc_attr($current_user->first_name); ?>">
            </div>
            <div class="form-group">
                <label for="last_name">Last Name</label>
                <input type="text" id="last_name" name="last_name" class="form-control" value="<?php echo esc_attr($current_user->last_name); ?>">
            </div>
            <div class="form-group">
                <label for="user_email">Email</label>
                <input type="email" id="user_email" name="user_email" class="form-control" value="<?php echo esc_attr($current_user->user_email); ?>">
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="hideProfileEdit()">Cancel</button>
                <button type="submit" class="btn btn-primary">Update Profile</button>
            </div>
        </form>
    </div>
</div>

<style>
    /* Dashboard Styles */
    .account-dashboard {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .dashboard-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 40px 20px;
        background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
        color: white;
        border-radius: 12px;
    }

    .dashboard-header h1 {
        font-size: 2.5em;
        margin-bottom: 15px;
        font-weight: bold;
    }

    .dashboard-header p {
        font-size: 1.1em;
        opacity: 0.9;
        margin: 0;
    }

    .dashboard-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
    }

    .stat-card {
        background: white;
        padding: 30px 20px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
        border: 1px solid #e9ecef;
    }

    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        color: #2c5aa0;
        margin-bottom: 10px;
    }

    .stat-label {
        color: #666;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.9em;
        letter-spacing: 0.5px;
    }

    .dashboard-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 40px;
    }

    .dashboard-section {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        border: 1px solid #e9ecef;
    }

    .dashboard-section h2 {
        margin-bottom: 25px;
        color: #333;
        font-size: 1.5em;
    }

    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .action-card {
        background: #f8f9fa;
        padding: 25px 20px;
        border-radius: 8px;
        text-align: center;
        text-decoration: none;
        color: #333;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .action-card:hover {
        background: #2c5aa0;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(44, 90, 160, 0.2);
    }

    .action-card i {
        font-size: 2.5em;
        margin-bottom: 15px;
        color: #2c5aa0;
    }

    .action-card:hover i {
        color: white;
    }

    .action-card h3 {
        margin-bottom: 10px;
        font-size: 1.1em;
    }

    .action-card p {
        margin: 0;
        font-size: 0.9em;
        opacity: 0.8;
    }

    .activity-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        background: #2c5aa0;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .activity-content h4 {
        margin: 0 0 5px 0;
        font-size: 1em;
        color: #333;
    }

    .activity-content p {
        margin: 0 0 5px 0;
        color: #666;
        font-size: 0.9em;
    }

    .activity-content small {
        color: #999;
        font-size: 0.8em;
    }

    .no-activity {
        text-align: center;
        padding: 40px 20px;
        color: #666;
    }

    .profile-card {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
        margin-bottom: 30px;
        border: 1px solid #e9ecef;
    }

    .profile-avatar {
        margin-bottom: 20px;
    }

    .profile-avatar img {
        border-radius: 50%;
        border: 4px solid #2c5aa0;
    }

    .profile-info h3 {
        margin-bottom: 10px;
        color: #333;
    }

    .profile-info p {
        margin-bottom: 5px;
        color: #666;
    }

    .profile-info small {
        color: #999;
    }

    .account-menu {
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }

    .account-menu h3 {
        margin-bottom: 20px;
        color: #333;
    }

    .account-menu ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .account-menu li {
        margin-bottom: 10px;
    }

    .account-menu a {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 15px;
        color: #666;
        text-decoration: none;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .account-menu a:hover {
        background: #2c5aa0;
        color: white;
        text-decoration: none;
    }

    /* Modal Styles */
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: white;
        padding: 0;
        border-radius: 12px;
        max-width: 500px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 30px;
        border-bottom: 1px solid #eee;
    }

    .modal-header h2 {
        margin: 0;
        color: #333;
    }

    .modal-close {
        font-size: 24px;
        cursor: pointer;
        color: #999;
    }

    .modal-close:hover {
        color: #333;
    }

    .modal form {
        padding: 30px;
    }

    .modal-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 30px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }

        .dashboard-stats {
            grid-template-columns: repeat(2, 1fr);
        }

        .action-grid {
            grid-template-columns: 1fr;
        }

        .dashboard-header h1 {
            font-size: 2em;
        }

        .stat-number {
            font-size: 2em;
        }
    }

    @media (max-width: 480px) {
        .dashboard-stats {
            grid-template-columns: 1fr;
        }

        .dashboard-header {
            padding: 30px 15px;
        }

        .dashboard-section {
            padding: 20px;
        }

        .profile-card,
        .account-menu {
            padding: 20px;
        }
    }
</style>

<script>
    jQuery(document).ready(function($) {
        // Logout functionality
        $('#logout-link').on('click', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to logout?')) {
                $.ajax({
                    url: videoAuth.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'video_logout',
                        nonce: videoAuth.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            window.location.href = response.data.redirect_url;
                        }
                    }
                });
            }
        });
    });

    // Profile edit functions
    function showProfileEdit() {
        document.getElementById('profile-modal').style.display = 'flex';
    }

    function hideProfileEdit() {
        document.getElementById('profile-modal').style.display = 'none';
    }

    // Close modal when clicking outside
    document.getElementById('profile-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideProfileEdit();
        }
    });

    // Close modal with X button
    document.querySelector('.modal-close').addEventListener('click', hideProfileEdit);
</script>

<?php get_footer(); ?>