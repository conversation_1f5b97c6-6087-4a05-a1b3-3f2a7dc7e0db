<?php

/**
 * Login Template
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();
?>

<div class="video-booking-container">
    <div class="auth-container">
        <div class="auth-form-wrapper">
            <div class="auth-header">
                <h1>Login to Your Account</h1>
                <p>Access your workshop recordings and manage your account</p>
            </div>

            <form id="login-form" class="auth-form">
                <?php wp_nonce_field('video_auth_nonce', 'nonce'); ?>

                <div class="form-group">
                    <label for="username">Username or Email *</label>
                    <input type="text" id="username" name="username" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="password">Password *</label>
                    <input type="password" id="password" name="password" class="form-control" required>
                </div>

                <div class="form-group form-check">
                    <label>
                        <input type="checkbox" id="remember" name="remember" value="1">
                        Remember me
                    </label>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-full-width">Login</button>
                </div>

                <div class="auth-links">
                    <p>Don't have an account? <a href="<?php echo home_url('/video-user-register'); ?>">Register here</a></p>
                    <p><a href="<?php echo wp_lostpassword_url(); ?>">Forgot your password?</a></p>
                </div>
            </form>
        </div>

        <div class="auth-benefits">
            <h3>Why Create an Account?</h3>
            <ul>
                <li><i class="fa fa-video-camera"></i> Access to exclusive workshop recordings</li>
                <li><i class="fa fa-clock-o"></i> Watch videos anytime, anywhere</li>
                <li><i class="fa fa-star"></i> Early bird pricing on new workshops</li>
                <li><i class="fa fa-user"></i> Personalized learning dashboard</li>
                <li><i class="fa fa-envelope"></i> Email notifications for new content</li>
            </ul>
        </div>
    </div>
</div>

<style>
    /* Auth Container Styles */
    .auth-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 60px;
        max-width: 1000px;
        margin: 60px auto;
        padding: 0 20px;
        align-items: start;
    }

    .auth-form-wrapper {
        background: white;
        padding: 40px;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }

    .auth-header {
        text-align: center;
        margin-bottom: 40px;
    }

    .auth-header h1 {
        font-size: 2em;
        margin-bottom: 15px;
        color: #333;
        font-weight: bold;
    }

    .auth-header p {
        color: #666;
        font-size: 1.1em;
        margin: 0;
    }

    .auth-form {
        max-width: 400px;
        margin: 0 auto;
    }

    .form-check {
        display: flex;
        align-items: center;
        margin-bottom: 25px;
    }

    .form-check label {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        font-weight: normal;
        margin-bottom: 0;
    }

    .form-check input[type="checkbox"] {
        margin: 0;
        width: auto;
    }

    .auth-links {
        text-align: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .auth-links p {
        margin-bottom: 10px;
        color: #666;
    }

    .auth-links a {
        color: #2c5aa0;
        text-decoration: none;
        font-weight: 600;
    }

    .auth-links a:hover {
        text-decoration: underline;
    }

    /* Benefits Section */
    .auth-benefits {
        background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
        color: white;
        padding: 40px;
        border-radius: 12px;
        height: fit-content;
    }

    .auth-benefits h3 {
        font-size: 1.5em;
        margin-bottom: 25px;
        text-align: center;
    }

    .auth-benefits ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .auth-benefits li {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
        font-size: 1.1em;
        line-height: 1.5;
    }

    .auth-benefits i {
        font-size: 1.3em;
        color: #ffc107;
        width: 20px;
        text-align: center;
    }

    /* Loading State */
    .auth-form.loading {
        opacity: 0.7;
        pointer-events: none;
    }

    .auth-form.loading button {
        position: relative;
    }

    .auth-form.loading button::before {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 16px;
        height: 16px;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: translate(-50%, -50%) rotate(0deg);
        }

        100% {
            transform: translate(-50%, -50%) rotate(360deg);
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .auth-container {
            grid-template-columns: 1fr;
            gap: 40px;
            margin: 40px auto;
        }

        .auth-form-wrapper {
            padding: 30px 20px;
        }

        .auth-header h1 {
            font-size: 1.7em;
        }

        .auth-benefits {
            padding: 30px 20px;
        }

        .auth-benefits li {
            font-size: 1em;
        }
    }

    @media (max-width: 480px) {
        .auth-container {
            padding: 0 15px;
            margin: 30px auto;
        }

        .auth-form-wrapper {
            padding: 25px 15px;
        }

        .auth-header h1 {
            font-size: 1.5em;
        }

        .auth-header p {
            font-size: 1em;
        }
    }

    /* Error States */
    .form-control.error {
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    .form-error {
        color: #dc3545;
        font-size: 13px;
        margin-top: 5px;
        display: block;
    }

    /* Success Message */
    .auth-success {
        background: #d4edda;
        color: #155724;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
        text-align: center;
        font-weight: bold;
    }

    /* Error Message */
    .auth-error {
        background: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
        text-align: center;
        font-weight: bold;
    }
</style>

<script>
    jQuery(document).ready(function($) {
        $('#login-form').on('submit', function(e) {
            e.preventDefault();

            const $form = $(this);
            const $submitButton = $form.find('button[type="submit"]');
            const originalText = $submitButton.text();

            // Clear previous errors
            $('.form-error').remove();
            $('.form-control').removeClass('error');
            $('.auth-error, .auth-success').remove();

            // Add loading state
            $form.addClass('loading');
            $submitButton.text('Logging in...');

            $.ajax({
                url: videoAuth.ajax_url,
                type: 'POST',
                data: $form.serialize() + '&action=video_login',
                success: function(response) {
                    if (response.success) {
                        $form.prepend('<div class="auth-success">' + response.data.message + '</div>');
                        setTimeout(() => {
                            window.location.href = response.data.redirect_url;
                        }, 1000);
                    } else {
                        $form.prepend('<div class="auth-error">' + response.data + '</div>');
                        $form.removeClass('loading');
                        $submitButton.text(originalText);
                    }
                },
                error: function() {
                    $form.prepend('<div class="auth-error">Network error. Please try again.</div>');
                    $form.removeClass('loading');
                    $submitButton.text(originalText);
                }
            });
        });
    });
</script>

<?php get_footer(); ?>