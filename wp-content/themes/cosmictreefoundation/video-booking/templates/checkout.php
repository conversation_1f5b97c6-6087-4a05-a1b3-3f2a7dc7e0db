<?php
/**
 * Video Checkout Template
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if cart is empty
$cart = VideoBookingCart::get_cart();
if (empty($cart)) {
    wp_redirect(home_url('/video-cart'));
    exit;
}

// Validate cart
$cart_errors = VideoBookingCart::validate_cart();
if (!empty($cart_errors)) {
    wp_redirect(home_url('/video-cart'));
    exit;
}

get_header();

$cart_summary = VideoBookingCart::get_cart_summary();
$applied_coupon = VideoBookingCoupons::get_applied_coupon();
?>

<div class="video-booking-container">
    <div class="checkout-container">
        <div class="checkout-header">
            <h1>Checkout</h1>
            <div class="checkout-steps">
                <span class="step active">1. Review Order</span>
                <span class="step active">2. Payment Details</span>
                <span class="step">3. Confirmation</span>
            </div>
        </div>
        
        <div class="checkout-content">
            <div class="checkout-main">
                <!-- Order Review -->
                <div class="checkout-section">
                    <h2>Order Review</h2>
                    <div class="order-items">
                        <?php foreach ($cart as $cart_key => $item): ?>
                            <div class="order-item">
                                <div class="item-thumbnail">
                                    <?php if ($item['thumbnail']): ?>
                                        <img src="<?php echo esc_url($item['thumbnail']); ?>" alt="<?php echo esc_attr($item['title']); ?>">
                                    <?php else: ?>
                                        <div class="no-thumbnail">
                                            <i class="fa fa-play-circle"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="item-details">
                                    <h3><?php echo esc_html($item['title']); ?></h3>
                                    <?php if ($item['is_early_bird']): ?>
                                        <span class="early-bird-badge">Early Bird</span>
                                        <p class="item-note">Available after workshop completion</p>
                                    <?php else: ?>
                                        <span class="instant-access-badge">Instant Access</span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="item-price">
                                    ₹<?php echo number_format($item['price'], 2); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Customer Information -->
                <div class="checkout-section">
                    <h2>Customer Information</h2>
                    <form id="checkout-form" class="video-checkout-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customer_name">Full Name *</label>
                                <input type="text" id="customer_name" name="customer_name" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="customer_email">Email Address *</label>
                                <input type="email" id="customer_email" name="customer_email" class="form-control" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="customer_phone">Phone Number</label>
                                <input type="tel" id="customer_phone" name="customer_phone" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="customer_city">City</label>
                                <input type="text" id="customer_city" name="customer_city" class="form-control">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="create_account" name="create_account" value="1">
                                Create an account for easy access to your videos
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="agree_terms" name="agree_terms" value="1" required>
                                I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a> *
                            </label>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="checkout-sidebar">
                <!-- Order Summary -->
                <div class="order-summary">
                    <h3>Order Summary</h3>
                    
                    <div class="summary-items">
                        <div class="summary-line">
                            <span>Items (<?php echo count($cart); ?>):</span>
                            <span>₹<?php echo $cart_summary['subtotal']; ?></span>
                        </div>
                        
                        <?php if ($applied_coupon): ?>
                            <div class="summary-line coupon-line">
                                <span>Coupon (<?php echo esc_html($applied_coupon['code']); ?>):</span>
                                <span class="discount">-₹<?php echo $cart_summary['discount']; ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="summary-line total-line">
                            <span><strong>Total:</strong></span>
                            <span class="total"><strong>₹<?php echo $cart_summary['total']; ?></strong></span>
                        </div>
                    </div>
                    
                    <div class="payment-section">
                        <h4>Payment Method</h4>
                        <div class="payment-method">
                            <div class="payment-option">
                                <input type="radio" id="razorpay" name="payment_method" value="razorpay" checked>
                                <label for="razorpay">
                                    <span class="payment-name">Credit/Debit Card, UPI, Net Banking</span>
                                    <span class="payment-provider">Powered by Razorpay</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="checkout-actions">
                        <?php if ($cart_summary['total'] == '0.00'): ?>
                            <button type="submit" form="checkout-form" class="btn btn-success btn-full-width checkout-submit">
                                <i class="fa fa-check"></i>
                                Complete Free Order
                            </button>
                        <?php else: ?>
                            <button type="submit" form="checkout-form" class="btn btn-success btn-full-width checkout-submit">
                                <i class="fa fa-lock"></i>
                                Complete Purchase - ₹<?php echo $cart_summary['total']; ?>
                            </button>
                        <?php endif; ?>
                        
                        <div class="security-badges">
                            <div class="badge">
                                <i class="fa fa-shield"></i>
                                <span>SSL Secured</span>
                            </div>
                            <div class="badge">
                                <i class="fa fa-lock"></i>
                                <span>256-bit Encryption</span>
                            </div>
                            <div class="badge">
                                <i class="fa fa-credit-card"></i>
                                <span>PCI Compliant</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Money Back Guarantee -->
                <div class="guarantee-section">
                    <div class="guarantee-badge">
                        <i class="fa fa-shield"></i>
                        <div class="guarantee-text">
                            <strong>30-Day Money Back Guarantee</strong>
                            <p>Not satisfied? Get a full refund within 30 days.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Support Info -->
                <div class="support-section">
                    <h4>Need Help?</h4>
                    <div class="support-options">
                        <div class="support-option">
                            <i class="fa fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="support-option">
                            <i class="fa fa-phone"></i>
                            <span>+91 XXX XXX XXXX</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Checkout Styles */
.checkout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.checkout-header {
    text-align: center;
    margin-bottom: 40px;
}

.checkout-header h1 {
    margin-bottom: 20px;
    color: #333;
}

.checkout-steps {
    display: flex;
    justify-content: center;
    gap: 30px;
}

.step {
    padding: 10px 20px;
    background: #f0f0f0;
    border-radius: 20px;
    font-size: 14px;
    color: #666;
}

.step.active {
    background: #2c5aa0;
    color: white;
}

.checkout-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

/* Checkout Sections */
.checkout-section {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.checkout-section h2 {
    margin: 0 0 25px 0;
    color: #333;
    border-bottom: 2px solid #eee;
    padding-bottom: 15px;
}

/* Order Items */
.order-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px 0;
    border-bottom: 1px solid #eee;
}

.order-item:last-child {
    border-bottom: none;
}

.item-thumbnail {
    width: 80px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
}

.item-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-details {
    flex: 1;
}

.item-details h3 {
    margin: 0 0 8px 0;
    font-size: 1.1em;
    color: #333;
}

.item-note {
    font-size: 13px;
    color: #666;
    margin: 5px 0 0 0;
}

.item-price {
    font-size: 1.2em;
    font-weight: bold;
    color: #2c5aa0;
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

/* Checkout Sidebar */
.order-summary,
.guarantee-section,
.support-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
}

.order-summary h3 {
    margin: 0 0 20px 0;
    color: #333;
}

.summary-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
}

.coupon-line {
    color: #28a745;
}

.total-line {
    border-top: 2px solid #eee;
    padding-top: 15px;
    font-size: 1.2em;
    color: #2c5aa0;
}

.payment-section {
    margin: 25px 0;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.payment-section h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.payment-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.payment-option label {
    flex: 1;
    cursor: pointer;
}

.payment-name {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

.payment-provider {
    font-size: 13px;
    color: #666;
}

.checkout-actions {
    margin-top: 25px;
}

.security-badges {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.security-badges .badge {
    text-align: center;
    font-size: 12px;
    color: #666;
}

.security-badges .badge i {
    display: block;
    font-size: 1.2em;
    margin-bottom: 5px;
    color: #28a745;
}

/* Guarantee Section */
.guarantee-badge {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.guarantee-badge i {
    font-size: 2em;
    color: #28a745;
}

.guarantee-text strong {
    display: block;
    color: #333;
    margin-bottom: 5px;
}

.guarantee-text p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

/* Support Section */
.support-section h4 {
    margin: 0 0 15px 0;
    color: #333;
}

.support-option {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.support-option i {
    color: #2c5aa0;
    width: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .checkout-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .checkout-steps {
        flex-direction: column;
        gap: 10px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .order-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .security-badges {
        flex-direction: column;
        gap: 15px;
    }
    
    .guarantee-badge {
        flex-direction: column;
        text-align: center;
    }
}
</style>

<?php get_footer(); ?>
