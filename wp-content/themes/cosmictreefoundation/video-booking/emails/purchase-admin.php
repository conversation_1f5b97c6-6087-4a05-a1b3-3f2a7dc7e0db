<?php
/**
 * Admin Purchase Notification Email Template
 */
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>New Video Purchase</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2c5aa0; color: white; padding: 15px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .order-info { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .btn { background: #2c5aa0; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>New Video Purchase</h2>
        </div>
        
        <div class="content">
            <p>A new video purchase has been made on <?php echo $site_name; ?>.</p>
            
            <div class="order-info">
                <h3>Order Information</h3>
                <p><strong>Order Number:</strong> <?php echo esc_html($order_number); ?></p>
                <p><strong>Customer:</strong> <?php echo esc_html($customer_name); ?></p>
                <p><strong>Email:</strong> <?php echo esc_html($customer_email); ?></p>
                <p><strong>Total Amount:</strong> ₹<?php echo number_format($total_amount, 2); ?></p>
                <p><strong>Order Date:</strong> <?php echo date("M j, Y g:i A", strtotime($order->created_at)); ?></p>
                
                <h4>Items Purchased:</h4>
                <?php foreach ($order->items as $item): ?>
                    <p>• <?php echo esc_html($item->title); ?> - ₹<?php echo number_format($item->price, 2); ?>
                    <?php if ($item->is_early_bird): ?>
                        <em>(Early Bird)</em>
                    <?php endif; ?>
                    </p>
                <?php endforeach; ?>
            </div>
            
            <p style="text-align: center;">
                <a href="<?php echo esc_url($admin_url); ?>" class="btn">View in Admin</a>
            </p>
        </div>
    </div>
</body>
</html>