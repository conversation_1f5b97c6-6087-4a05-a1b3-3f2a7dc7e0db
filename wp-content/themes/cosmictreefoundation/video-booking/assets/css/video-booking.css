/**
 * Video Booking System Styles
 * 
 * @package VideoBooking
 */

/* Reset and Base Styles */
.video-booking-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.video-booking-container * {
    box-sizing: border-box;
}

/* Video Grid Layout */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin: 30px 0;
}

.video-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.video-card:hover .video-thumbnail img {
    transform: scale(1.05);
}

.early-bird-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff6b35;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.video-info {
    padding: 20px;
}

.video-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
    line-height: 1.4;
}

.video-description {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-pricing {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.price-regular {
    font-size: 20px;
    font-weight: bold;
    color: #2c5aa0;
}

.price-early-bird {
    font-size: 16px;
    color: #ff6b35;
    text-decoration: line-through;
    margin-left: 10px;
}

.early-bird-note {
    font-size: 12px;
    color: #ff6b35;
    font-style: italic;
    margin-top: 5px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-primary {
    background: #2c5aa0;
    color: white;
}

.btn-primary:hover {
    background: #1e3f73;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
    color: white;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    color: #212529;
}

.btn-full-width {
    width: 100%;
}

/* Cart Styles */
.cart-container {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
    margin: 20px 0;
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cart-item-thumbnail {
    width: 80px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    margin-right: 15px;
}

.cart-item-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.cart-item-price {
    color: #2c5aa0;
    font-weight: bold;
}

.cart-item-remove {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.cart-item-remove:hover {
    background: #c82333;
}

.cart-summary {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

.cart-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    color: #2c5aa0;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 2px solid #e9ecef;
}

/* Coupon Form */
.coupon-form {
    display: flex;
    gap: 10px;
    margin: 15px 0;
}

.coupon-input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.coupon-apply {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.coupon-apply:hover {
    background: #1e7e34;
}

.coupon-success {
    color: #28a745;
    font-size: 14px;
    margin-top: 5px;
}

.coupon-error {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.1);
}

.form-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
}

/* Video Player Styles */
.video-player-container {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    margin: 20px 0;
}

.video-player {
    width: 100%;
    height: auto;
    min-height: 400px;
}

.video-controls-disabled {
    pointer-events: none;
}

/* Access Messages */
.access-message {
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    text-align: center;
}

.access-message.expired {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.access-message.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.access-message.granted {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .video-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .video-booking-container {
        padding: 15px;
    }
    
    .cart-item {
        flex-direction: column;
        text-align: center;
    }
    
    .cart-item-thumbnail {
        margin: 0 0 15px 0;
    }
    
    .coupon-form {
        flex-direction: column;
    }
    
    .video-pricing {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .price-early-bird {
        margin-left: 0;
        margin-top: 5px;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2c5aa0;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }
