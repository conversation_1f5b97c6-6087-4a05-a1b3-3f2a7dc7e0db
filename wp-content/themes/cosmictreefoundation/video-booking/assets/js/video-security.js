/**
 * Video Security JavaScript
 * 
 * @package VideoBooking
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Video Security Object
    const VideoSecurity = {
        
        // Security state
        violations: 0,
        maxViolations: 5,
        warningShown: false,
        monitoringActive: false,
        
        // Initialize security
        init: function() {
            this.enableProtections();
            this.startMonitoring();
            this.protectVideoElements();
            this.setupEventListeners();
        },
        
        // Enable basic protections
        enableProtections: function() {
            // Disable right-click context menu
            $(document).on('contextmenu', function(e) {
                if ($(e.target).is('video') || $(e.target).closest('.video-player-container').length) {
                    e.preventDefault();
                    VideoSecurity.logViolation('right_click_blocked');
                    return false;
                }
            });
            
            // Disable text selection on video elements
            $('.video-player-container').css({
                '-webkit-user-select': 'none',
                '-moz-user-select': 'none',
                '-ms-user-select': 'none',
                'user-select': 'none',
                '-webkit-touch-callout': 'none'
            });
            
            // Disable drag and drop
            $(document).on('dragstart', 'video', function(e) {
                e.preventDefault();
                VideoSecurity.logViolation('drag_blocked');
                return false;
            });
            
            // Block common keyboard shortcuts
            $(document).on('keydown', function(e) {
                const blockedCombinations = [
                    {key: 123}, // F12
                    {key: 73, ctrl: true, shift: true}, // Ctrl+Shift+I
                    {key: 74, ctrl: true, shift: true}, // Ctrl+Shift+J
                    {key: 85, ctrl: true}, // Ctrl+U (View Source)
                    {key: 83, ctrl: true}, // Ctrl+S (Save)
                    {key: 80, ctrl: true}, // Ctrl+P (Print)
                    {key: 67, ctrl: true}, // Ctrl+C (Copy)
                    {key: 86, ctrl: true}, // Ctrl+V (Paste)
                    {key: 65, ctrl: true}, // Ctrl+A (Select All)
                    {key: 44} // Print Screen
                ];
                
                for (let combo of blockedCombinations) {
                    if (e.keyCode === combo.key &&
                        (!combo.ctrl || e.ctrlKey) &&
                        (!combo.shift || e.shiftKey) &&
                        (!combo.alt || e.altKey)) {
                        e.preventDefault();
                        VideoSecurity.logViolation('keyboard_shortcut_blocked', {
                            keyCode: e.keyCode,
                            ctrl: e.ctrlKey,
                            shift: e.shiftKey,
                            alt: e.altKey
                        });
                        return false;
                    }
                }
            });
            
            // Disable print
            window.addEventListener('beforeprint', function(e) {
                e.preventDefault();
                VideoSecurity.logViolation('print_blocked');
                return false;
            });
        },
        
        // Start monitoring for suspicious activity
        startMonitoring: function() {
            this.monitoringActive = true;
            
            // Monitor for developer tools
            this.detectDevTools();
            
            // Monitor for screen recording
            this.detectScreenRecording();
            
            // Monitor window focus changes
            this.monitorFocusChanges();
            
            // Monitor for unusual activity patterns
            this.monitorActivityPatterns();
        },
        
        // Detect developer tools
        detectDevTools: function() {
            let devToolsOpen = false;
            
            const detectLoop = () => {
                if (!this.monitoringActive) return;
                
                const threshold = 160;
                const widthDiff = window.outerWidth - window.innerWidth;
                const heightDiff = window.outerHeight - window.innerHeight;
                
                if (widthDiff > threshold || heightDiff > threshold) {
                    if (!devToolsOpen) {
                        devToolsOpen = true;
                        this.logViolation('devtools_detected', {
                            widthDiff: widthDiff,
                            heightDiff: heightDiff
                        });
                    }
                } else {
                    devToolsOpen = false;
                }
                
                setTimeout(detectLoop, 1000);
            };
            
            detectLoop();
        },
        
        // Detect screen recording attempts
        detectScreenRecording: function() {
            // Monitor for getDisplayMedia API usage
            if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia;
                navigator.mediaDevices.getDisplayMedia = function() {
                    VideoSecurity.logViolation('screen_capture_api_used');
                    return originalGetDisplayMedia.apply(this, arguments);
                };
            }
            
            // Monitor for getUserMedia with screen capture
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
                navigator.mediaDevices.getUserMedia = function(constraints) {
                    if (constraints && constraints.video && constraints.video.mediaSource) {
                        VideoSecurity.logViolation('screen_capture_detected', {
                            mediaSource: constraints.video.mediaSource
                        });
                    }
                    return originalGetUserMedia.apply(this, arguments);
                };
            }
        },
        
        // Monitor focus changes
        monitorFocusChanges: function() {
            let focusChanges = 0;
            let lastFocusChange = Date.now();
            let blurTimeout;
            
            $(window).on('blur', function() {
                focusChanges++;
                const now = Date.now();
                
                // Detect rapid focus changes (possible automation)
                if (now - lastFocusChange < 1000 && focusChanges > 10) {
                    VideoSecurity.logViolation('rapid_focus_changes', {
                        changes: focusChanges,
                        timespan: now - lastFocusChange
                    });
                }
                
                lastFocusChange = now;
                
                // Blur video after 3 seconds of window being unfocused
                blurTimeout = setTimeout(() => {
                    $('.video-player-container').css('filter', 'blur(10px)');
                }, 3000);
            });
            
            $(window).on('focus', function() {
                clearTimeout(blurTimeout);
                $('.video-player-container').css('filter', 'none');
            });
        },
        
        // Monitor activity patterns
        monitorActivityPatterns: function() {
            let mouseMovements = 0;
            let keyPresses = 0;
            let lastActivity = Date.now();
            
            // Track mouse movements
            $(document).on('mousemove', function() {
                mouseMovements++;
                lastActivity = Date.now();
            });
            
            // Track key presses
            $(document).on('keypress', function() {
                keyPresses++;
                lastActivity = Date.now();
            });
            
            // Check for bot-like behavior every 30 seconds
            setInterval(() => {
                const now = Date.now();
                const timeSinceLastActivity = now - lastActivity;
                
                // If no activity for 5 minutes but video is still playing
                const video = $('video')[0];
                if (video && !video.paused && timeSinceLastActivity > 300000) {
                    this.logViolation('suspicious_inactivity', {
                        timeSinceLastActivity: timeSinceLastActivity,
                        videoCurrentTime: video.currentTime
                    });
                }
                
                // Reset counters
                mouseMovements = 0;
                keyPresses = 0;
            }, 30000);
        },
        
        // Protect video elements
        protectVideoElements: function() {
            $('video').each(function() {
                const video = this;
                
                // Remove download attribute
                video.removeAttribute('download');
                
                // Set control list to disable download
                video.setAttribute('controlsList', 'nodownload nofullscreen noremoteplayback');
                
                // Disable picture-in-picture
                video.disablePictureInPicture = true;
                
                // Monitor for attribute changes
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes') {
                            // Re-apply protections if attributes are modified
                            video.removeAttribute('download');
                            video.setAttribute('controlsList', 'nodownload nofullscreen noremoteplayback');
                            video.disablePictureInPicture = true;
                        }
                    });
                });
                
                observer.observe(video, {
                    attributes: true,
                    attributeFilter: ['download', 'controlsList', 'disablePictureInPicture']
                });
                
                // Monitor for video capture attempts
                if (video.captureStream) {
                    const originalCaptureStream = video.captureStream;
                    video.captureStream = function() {
                        VideoSecurity.logViolation('video_capture_stream_used');
                        return originalCaptureStream.apply(this, arguments);
                    };
                }
                
                // Monitor video events
                $(video).on('loadstart', function() {
                    console.log('Video security monitoring active');
                });
                
                $(video).on('play', function() {
                    VideoSecurity.logActivity('video_play_started');
                });
                
                $(video).on('pause', function() {
                    VideoSecurity.logActivity('video_paused');
                });
                
                $(video).on('ended', function() {
                    VideoSecurity.logActivity('video_ended');
                });
            });
        },
        
        // Setup additional event listeners
        setupEventListeners: function() {
            // Monitor for console access attempts
            let consoleWarningShown = false;
            const originalConsole = window.console;
            
            // Override console methods to detect usage
            ['log', 'warn', 'error', 'info', 'debug'].forEach(method => {
                const original = originalConsole[method];
                originalConsole[method] = function() {
                    if (!consoleWarningShown) {
                        consoleWarningShown = true;
                        VideoSecurity.logViolation('console_access_detected');
                    }
                    return original.apply(this, arguments);
                };
            });
            
            // Monitor for page visibility changes
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    VideoSecurity.logActivity('page_hidden');
                } else {
                    VideoSecurity.logActivity('page_visible');
                }
            });
            
            // Monitor for fullscreen changes
            document.addEventListener('fullscreenchange', function() {
                if (document.fullscreenElement) {
                    VideoSecurity.logActivity('fullscreen_entered');
                } else {
                    VideoSecurity.logActivity('fullscreen_exited');
                }
            });
        },
        
        // Log security violation
        logViolation: function(type, details = {}) {
            this.violations++;
            
            console.warn('Security violation detected:', type, details);
            
            // Send to server
            this.sendToServer('violation', type, details);
            
            // Show warning if threshold reached
            if (this.violations >= this.maxViolations && !this.warningShown) {
                this.showSecurityWarning();
            }
        },
        
        // Log activity (non-violation)
        logActivity: function(type, details = {}) {
            this.sendToServer('activity', type, details);
        },
        
        // Send data to server
        sendToServer: function(category, type, details = {}) {
            if (typeof videoSecurity === 'undefined') return;
            
            $.ajax({
                url: videoSecurity.ajax_url,
                type: 'POST',
                data: {
                    action: 'log_suspicious_activity',
                    nonce: videoSecurity.nonce,
                    category: category,
                    activity_type: type,
                    details: JSON.stringify(details),
                    timestamp: Date.now(),
                    user_agent: navigator.userAgent,
                    screen_resolution: screen.width + 'x' + screen.height,
                    window_size: window.innerWidth + 'x' + window.innerHeight,
                    video_current_time: this.getCurrentVideoTime()
                },
                success: function(response) {
                    // Handle server response if needed
                },
                error: function() {
                    console.warn('Failed to log security event');
                }
            });
        },
        
        // Get current video time
        getCurrentVideoTime: function() {
            const video = $('video')[0];
            return video ? video.currentTime : 0;
        },
        
        // Show security warning
        showSecurityWarning: function() {
            if (this.warningShown) return;
            
            this.warningShown = true;
            
            const warning = $(`
                <div id="security-warning" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    background: #dc3545;
                    color: white;
                    padding: 15px;
                    text-align: center;
                    z-index: 99999;
                    font-weight: bold;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                ">
                    <i class="fa fa-exclamation-triangle"></i>
                    Security Warning: Unauthorized activity detected. Video access may be restricted.
                    <button id="dismiss-warning" style="
                        background: rgba(255,255,255,0.2);
                        border: 1px solid rgba(255,255,255,0.3);
                        color: white;
                        padding: 5px 10px;
                        margin-left: 15px;
                        border-radius: 3px;
                        cursor: pointer;
                    ">Dismiss</button>
                </div>
            `);
            
            $('body').prepend(warning);
            
            // Auto-dismiss after 15 seconds
            setTimeout(() => {
                warning.fadeOut(500, function() {
                    $(this).remove();
                });
            }, 15000);
            
            // Manual dismiss
            $('#dismiss-warning').on('click', function() {
                warning.fadeOut(500, function() {
                    $(this).remove();
                });
            });
        },
        
        // Cleanup when leaving page
        cleanup: function() {
            this.monitoringActive = false;
            this.logActivity('security_monitoring_stopped');
        }
    };
    
    // Initialize security when DOM is ready
    VideoSecurity.init();
    
    // Cleanup on page unload
    $(window).on('beforeunload', function() {
        VideoSecurity.cleanup();
    });
    
    // Make VideoSecurity globally available for debugging (in development)
    if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
        window.VideoSecurity = VideoSecurity;
    }
});
