/**
 * Video Booking Authentication JavaScript
 * 
 * @package VideoBooking
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Authentication Object
    const VideoAuth = {
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.setupLogoutHandler();
        },
        
        // Bind events
        bindEvents: function() {
            // Login form
            $(document).on('submit', '#login-form', this.handleLogin);
            
            // Registration form
            $(document).on('submit', '#register-form', this.handleRegistration);
            
            // Profile update form
            $(document).on('submit', '#profile-form', this.handleProfileUpdate);
            
            // Password change form
            $(document).on('submit', '#password-form', this.handlePasswordChange);
            
            // Logout links
            $(document).on('click', '#logout-link, .logout-link', this.handleLogout);
            
            // Modal controls
            $(document).on('click', '.modal-close', this.closeModal);
            $(document).on('click', '.modal', this.closeModalOnBackdrop);
        },
        
        // Handle login
        handleLogin: function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $submitButton = $form.find('button[type="submit"]');
            const originalText = $submitButton.text();
            
            // Clear previous errors
            VideoAuth.clearFormErrors($form);
            
            // Basic validation
            if (!VideoAuth.validateLoginForm($form)) {
                return;
            }
            
            // Add loading state
            $form.addClass('loading');
            $submitButton.prop('disabled', true).text('Logging in...');
            
            $.ajax({
                url: videoAuth.ajax_url,
                type: 'POST',
                data: $form.serialize() + '&action=video_login',
                success: function(response) {
                    if (response.success) {
                        VideoAuth.showMessage('Login successful! Redirecting...', 'success');
                        setTimeout(() => {
                            const redirectUrl = VideoAuth.getRedirectUrl() || response.data.redirect_url;
                            window.location.href = redirectUrl;
                        }, 1000);
                    } else {
                        VideoAuth.showFormError($form, response.data);
                        $form.removeClass('loading');
                        $submitButton.prop('disabled', false).text(originalText);
                    }
                },
                error: function() {
                    VideoAuth.showFormError($form, 'Network error. Please try again.');
                    $form.removeClass('loading');
                    $submitButton.prop('disabled', false).text(originalText);
                }
            });
        },
        
        // Handle registration
        handleRegistration: function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $submitButton = $form.find('button[type="submit"]');
            const originalText = $submitButton.text();
            
            // Clear previous errors
            VideoAuth.clearFormErrors($form);
            
            // Validate form
            if (!VideoAuth.validateRegistrationForm($form)) {
                return;
            }
            
            // Add loading state
            $form.addClass('loading');
            $submitButton.prop('disabled', true).text('Creating Account...');
            
            $.ajax({
                url: videoAuth.ajax_url,
                type: 'POST',
                data: $form.serialize() + '&action=video_register',
                success: function(response) {
                    if (response.success) {
                        VideoAuth.showMessage('Registration successful! Welcome!', 'success');
                        setTimeout(() => {
                            const redirectUrl = VideoAuth.getRedirectUrl() || response.data.redirect_url;
                            window.location.href = redirectUrl;
                        }, 2000);
                    } else {
                        VideoAuth.showFormError($form, response.data);
                        $form.removeClass('loading');
                        $submitButton.prop('disabled', false).text(originalText);
                    }
                },
                error: function() {
                    VideoAuth.showFormError($form, 'Network error. Please try again.');
                    $form.removeClass('loading');
                    $submitButton.prop('disabled', false).text(originalText);
                }
            });
        },
        
        // Handle profile update
        handleProfileUpdate: function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $submitButton = $form.find('button[type="submit"]');
            const originalText = $submitButton.text();
            
            // Clear previous errors
            VideoAuth.clearFormErrors($form);
            
            // Add loading state
            $submitButton.prop('disabled', true).text('Updating...');
            
            $.ajax({
                url: videoAuth.ajax_url,
                type: 'POST',
                data: $form.serialize() + '&action=video_update_profile',
                success: function(response) {
                    if (response.success) {
                        VideoAuth.showMessage('Profile updated successfully!', 'success');
                        VideoAuth.closeModal();
                        // Refresh page to show updated info
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        VideoAuth.showFormError($form, response.data);
                        $submitButton.prop('disabled', false).text(originalText);
                    }
                },
                error: function() {
                    VideoAuth.showFormError($form, 'Network error. Please try again.');
                    $submitButton.prop('disabled', false).text(originalText);
                }
            });
        },
        
        // Handle logout
        handleLogout: function(e) {
            e.preventDefault();
            
            if (!confirm('Are you sure you want to logout?')) {
                return;
            }
            
            $.ajax({
                url: videoAuth.ajax_url,
                type: 'POST',
                data: {
                    action: 'video_logout',
                    nonce: videoAuth.nonce
                },
                success: function(response) {
                    if (response.success) {
                        VideoAuth.showMessage('Logged out successfully', 'success');
                        setTimeout(() => {
                            window.location.href = response.data.redirect_url;
                        }, 1000);
                    }
                },
                error: function() {
                    VideoAuth.showMessage('Logout failed. Please try again.', 'error');
                }
            });
        },
        
        // Validate login form
        validateLoginForm: function($form) {
            let isValid = true;
            
            const username = $form.find('#username').val().trim();
            const password = $form.find('#password').val();
            
            if (!username) {
                VideoAuth.showFieldError($form.find('#username'), 'Username or email is required');
                isValid = false;
            }
            
            if (!password) {
                VideoAuth.showFieldError($form.find('#password'), 'Password is required');
                isValid = false;
            }
            
            return isValid;
        },
        
        // Validate registration form
        validateRegistrationForm: function($form) {
            let isValid = true;
            
            const firstName = $form.find('#first_name').val().trim();
            const lastName = $form.find('#last_name').val().trim();
            const username = $form.find('#username').val().trim();
            const email = $form.find('#email').val().trim();
            const password = $form.find('#password').val();
            const confirmPassword = $form.find('#confirm_password').val();
            const agreeTerms = $form.find('#agree_terms').is(':checked');
            
            // Required fields
            if (!firstName) {
                VideoAuth.showFieldError($form.find('#first_name'), 'First name is required');
                isValid = false;
            }
            
            if (!lastName) {
                VideoAuth.showFieldError($form.find('#last_name'), 'Last name is required');
                isValid = false;
            }
            
            if (!username) {
                VideoAuth.showFieldError($form.find('#username'), 'Username is required');
                isValid = false;
            } else if (!/^[a-zA-Z0-9_]+$/.test(username)) {
                VideoAuth.showFieldError($form.find('#username'), 'Username can only contain letters, numbers, and underscores');
                isValid = false;
            }
            
            if (!email) {
                VideoAuth.showFieldError($form.find('#email'), 'Email is required');
                isValid = false;
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                VideoAuth.showFieldError($form.find('#email'), 'Please enter a valid email address');
                isValid = false;
            }
            
            if (!password) {
                VideoAuth.showFieldError($form.find('#password'), 'Password is required');
                isValid = false;
            } else if (password.length < 6) {
                VideoAuth.showFieldError($form.find('#password'), 'Password must be at least 6 characters long');
                isValid = false;
            }
            
            if (!confirmPassword) {
                VideoAuth.showFieldError($form.find('#confirm_password'), 'Please confirm your password');
                isValid = false;
            } else if (password !== confirmPassword) {
                VideoAuth.showFieldError($form.find('#confirm_password'), 'Passwords do not match');
                isValid = false;
            }
            
            if (!agreeTerms) {
                VideoAuth.showFieldError($form.find('#agree_terms'), 'You must agree to the terms and conditions');
                isValid = false;
            }
            
            return isValid;
        },
        
        // Show field error
        showFieldError: function($field, message) {
            $field.addClass('error');
            
            // Remove existing error
            $field.siblings('.form-error').remove();
            
            // Add new error
            $field.after('<span class="form-error">' + message + '</span>');
        },
        
        // Show form error
        showFormError: function($form, message) {
            // Remove existing messages
            $form.find('.auth-error, .auth-success').remove();
            
            // Add error message
            $form.prepend('<div class="auth-error">' + message + '</div>');
        },
        
        // Clear form errors
        clearFormErrors: function($form) {
            $form.find('.form-error').remove();
            $form.find('.form-control').removeClass('error');
            $form.find('.auth-error, .auth-success').remove();
        },
        
        // Show message
        showMessage: function(message, type = 'info') {
            // Remove existing messages
            $('.video-message').remove();
            
            const $message = $(`
                <div class="video-message video-message-${type}">
                    ${message}
                </div>
            `);
            
            $('body').append($message);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                $message.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        },
        
        // Get redirect URL from query parameter
        getRedirectUrl: function() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('redirect_to');
        },
        
        // Setup logout handler for menu items
        setupLogoutHandler: function() {
            // Handle logout from navigation menu
            $(document).on('click', '.menu-item-logout a', function(e) {
                e.preventDefault();
                VideoAuth.handleLogout.call(this, e);
            });
        },
        
        // Close modal
        closeModal: function() {
            $('.modal').hide();
        },
        
        // Close modal on backdrop click
        closeModalOnBackdrop: function(e) {
            if (e.target === this) {
                VideoAuth.closeModal();
            }
        }
    };
    
    // Initialize VideoAuth
    VideoAuth.init();
    
    // Make VideoAuth globally available
    window.VideoAuth = VideoAuth;
    
    // Password strength indicator
    $(document).on('input', '#password', function() {
        const password = $(this).val();
        let $strengthIndicator = $(this).siblings('.password-strength');
        
        if (!$strengthIndicator.length) {
            $(this).after('<div class="password-strength"><div class="password-strength-bar"></div></div>');
            $strengthIndicator = $(this).siblings('.password-strength');
        }
        
        let strength = 'weak';
        if (password.length >= 8 && /[A-Z]/.test(password) && /[0-9]/.test(password) && /[!@#$%^&*]/.test(password)) {
            strength = 'strong';
        } else if (password.length >= 6 && (/[A-Z]/.test(password) || /[0-9]/.test(password))) {
            strength = 'medium';
        }
        
        $strengthIndicator.removeClass('weak medium strong').addClass(strength);
    });
    
    // Real-time username validation
    $(document).on('input', '#username', function() {
        const username = $(this).val();
        const $field = $(this);
        
        // Clear previous errors
        $field.removeClass('error');
        $field.siblings('.form-error').remove();
        
        if (username && !/^[a-zA-Z0-9_]+$/.test(username)) {
            VideoAuth.showFieldError($field, 'Username can only contain letters, numbers, and underscores');
        }
    });
    
    // Real-time password confirmation
    $(document).on('input', '#confirm_password', function() {
        const password = $('#password').val();
        const confirmPassword = $(this).val();
        const $field = $(this);
        
        // Clear previous errors
        $field.removeClass('error');
        $field.siblings('.form-error').remove();
        
        if (confirmPassword && password !== confirmPassword) {
            VideoAuth.showFieldError($field, 'Passwords do not match');
        }
    });
});
