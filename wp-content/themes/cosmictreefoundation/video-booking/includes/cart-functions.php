<?php

/**
 * Cart Functions
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Cart Functions Class
 */
class VideoBookingCart
{

    /**
     * Initialize cart session
     */
    public static function init_session()
    {
        if (!session_id()) {
            session_start();
        }

        if (!isset($_SESSION['video_cart'])) {
            $_SESSION['video_cart'] = array();
        }
    }

    /**
     * Add video to cart
     */
    public static function add_to_cart($video_id, $is_early_bird = false)
    {
        self::init_session();

        $video = VideoBookingDB::get_video($video_id);
        if (!$video || !$video->is_available) {
            return array(
                'success' => false,
                'message' => 'Video not found or not available'
            );
        }

        // Check if early bird pricing has expired
        if ($is_early_bird && $video->early_bird_end_date) {
            $is_early_bird_expired = strtotime($video->early_bird_end_date) <= current_time('timestamp');
            if ($is_early_bird_expired) {
                $is_early_bird = false; // Force regular pricing
            }
        }

        // Check if already in cart
        $cart_key = $video_id . '_' . ($is_early_bird ? 'early' : 'regular');
        if (isset($_SESSION['video_cart'][$cart_key])) {
            return array(
                'success' => false,
                'message' => 'Video is already in your cart'
            );
        }

        // Determine price
        $price = $is_early_bird ? $video->early_bird_price : $video->regular_price;

        // Add to cart
        $_SESSION['video_cart'][$cart_key] = array(
            'video_id' => $video_id,
            'title' => $video->title,
            'thumbnail' => $video->thumbnail,
            'price' => $price,
            'is_early_bird' => $is_early_bird,
            'added_at' => current_time('mysql')
        );

        return array(
            'success' => true,
            'message' => 'Video added to cart successfully'
        );
    }

    /**
     * Remove video from cart
     */
    public static function remove_from_cart($video_id, $is_early_bird = false)
    {
        self::init_session();

        $cart_key = $video_id . '_' . ($is_early_bird ? 'early' : 'regular');

        if (isset($_SESSION['video_cart'][$cart_key])) {
            unset($_SESSION['video_cart'][$cart_key]);
            return array(
                'success' => true,
                'message' => 'Video removed from cart'
            );
        }

        return array(
            'success' => false,
            'message' => 'Video not found in cart'
        );
    }

    /**
     * Get cart contents
     */
    public static function get_cart()
    {
        self::init_session();

        return $_SESSION['video_cart'];
    }

    /**
     * Get cart count
     */
    public static function get_cart_count()
    {
        $cart = self::get_cart();
        return count($cart);
    }

    /**
     * Get cart total
     */
    public static function get_cart_total()
    {
        $cart = self::get_cart();
        $total = 0;

        foreach ($cart as $item) {
            $total += $item['price'];
        }

        return $total;
    }

    /**
     * Get cart total with discount
     */
    public static function get_cart_total_with_discount()
    {
        $subtotal = self::get_cart_total();
        $coupon = VideoBookingCoupons::get_applied_coupon();

        if ($coupon) {
            $discount = $coupon['discount_amount'];
            return max(0, $subtotal - $discount);
        }

        return $subtotal;
    }



    /**
     * Clear cart
     */
    public static function clear_cart()
    {
        self::init_session();
        $_SESSION['video_cart'] = array();

        // Also remove applied coupon
        VideoBookingCoupons::remove_coupon();
    }

    /**
     * Validate cart items
     */
    public static function validate_cart()
    {
        $cart = self::get_cart();
        $errors = array();

        if (empty($cart)) {
            $errors[] = 'Your cart is empty';
            return $errors;
        }

        foreach ($cart as $cart_key => $item) {
            $video = VideoBookingDB::get_video($item['video_id']);

            if (!$video) {
                $errors[] = 'Video "' . $item['title'] . '" is no longer available';
                continue;
            }

            if (!$video->is_available) {
                $errors[] = 'Video "' . $video->title . '" is currently unavailable';
                continue;
            }

            // Check if price has changed
            $current_price = $item['is_early_bird'] ? $video->early_bird_price : $video->regular_price;
            if ($current_price != $item['price']) {
                $errors[] = 'Price for "' . $video->title . '" has changed. Please refresh your cart.';
            }
        }

        return $errors;
    }

    /**
     * Get cart summary for display
     */
    public static function get_cart_summary()
    {
        $cart = self::get_cart();
        $subtotal = self::get_cart_total();
        $coupon = VideoBookingCoupons::get_applied_coupon();
        $discount = $coupon ? $coupon['discount_amount'] : 0;
        $total = $subtotal - $discount;

        return array(
            'count' => count($cart),
            'subtotal' => number_format($subtotal, 2),
            'discount' => number_format($discount, 2),
            'total' => number_format($total, 2),
            'coupon' => $coupon
        );
    }
}

// AJAX handlers for cart functionality
add_action('wp_ajax_video_add_to_cart', 'video_add_to_cart_ajax');
add_action('wp_ajax_nopriv_video_add_to_cart', 'video_add_to_cart_ajax');

function video_add_to_cart_ajax()
{
    check_ajax_referer('video_booking_nonce', 'nonce');

    $video_id = absint($_POST['video_id']);
    $is_early_bird = isset($_POST['is_early_bird']) && $_POST['is_early_bird'] == '1';

    $result = VideoBookingCart::add_to_cart($video_id, $is_early_bird);

    if ($result['success']) {
        wp_send_json_success($result['message']);
    } else {
        wp_send_json_error($result['message']);
    }
}

add_action('wp_ajax_video_remove_from_cart', 'video_remove_from_cart_ajax');
add_action('wp_ajax_nopriv_video_remove_from_cart', 'video_remove_from_cart_ajax');

function video_remove_from_cart_ajax()
{
    check_ajax_referer('video_booking_nonce', 'nonce');

    $video_id = absint($_POST['video_id']);
    $is_early_bird = isset($_POST['is_early_bird']) && $_POST['is_early_bird'] == '1';

    $result = VideoBookingCart::remove_from_cart($video_id, $is_early_bird);

    if ($result['success']) {
        wp_send_json_success($result['message']);
    } else {
        wp_send_json_error($result['message']);
    }
}

add_action('wp_ajax_video_get_cart', 'video_get_cart_ajax');
add_action('wp_ajax_nopriv_video_get_cart', 'video_get_cart_ajax');

function video_get_cart_ajax()
{
    check_ajax_referer('video_booking_nonce', 'nonce');

    $summary = VideoBookingCart::get_cart_summary();

    wp_send_json_success($summary);
}

add_action('wp_ajax_video_clear_cart', 'video_clear_cart_ajax');
add_action('wp_ajax_nopriv_video_clear_cart', 'video_clear_cart_ajax');

function video_clear_cart_ajax()
{
    check_ajax_referer('video_booking_nonce', 'nonce');

    VideoBookingCart::clear_cart();

    wp_send_json_success('Cart cleared');
}

add_action('wp_ajax_video_validate_cart', 'video_validate_cart_ajax');
add_action('wp_ajax_nopriv_video_validate_cart', 'video_validate_cart_ajax');

function video_validate_cart_ajax()
{
    check_ajax_referer('video_booking_nonce', 'nonce');

    $errors = VideoBookingCart::validate_cart();

    if (empty($errors)) {
        wp_send_json_success('Cart is valid');
    } else {
        wp_send_json_error($errors);
    }
}
