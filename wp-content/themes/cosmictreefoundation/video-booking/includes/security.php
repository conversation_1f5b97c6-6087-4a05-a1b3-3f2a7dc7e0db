<?php
/**
 * Security Functions
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Security Class
 */
class VideoBookingSecurity {
    
    /**
     * Initialize security features
     */
    public static function init() {
        // Add security headers
        add_action('wp_head', array(__CLASS__, 'add_security_headers'));
        
        // Enqueue security scripts
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_security_scripts'));
        
        // Block direct access to video files
        add_action('init', array(__CLASS__, 'block_direct_video_access'));
        
        // Add content security policy
        add_action('send_headers', array(__CLASS__, 'add_content_security_policy'));
        
        // Monitor suspicious activity
        add_action('wp_footer', array(__CLASS__, 'add_activity_monitoring'));
    }
    
    /**
     * Add security headers
     */
    public static function add_security_headers() {
        // Only add on video pages
        $video_page = get_query_var('video_page');
        if (!in_array($video_page, array('stream', 'my_recordings'))) {
            return;
        }
        
        ?>
        <meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex">
        <meta http-equiv="X-Frame-Options" content="SAMEORIGIN">
        <meta http-equiv="X-Content-Type-Options" content="nosniff">
        <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
        <?php
    }
    
    /**
     * Add Content Security Policy
     */
    public static function add_content_security_policy() {
        $video_page = get_query_var('video_page');
        if ($video_page === 'stream') {
            $csp = "default-src 'self'; ";
            $csp .= "script-src 'self' 'unsafe-inline' 'unsafe-eval'; ";
            $csp .= "style-src 'self' 'unsafe-inline'; ";
            $csp .= "media-src 'self' blob:; ";
            $csp .= "img-src 'self' data:; ";
            $csp .= "connect-src 'self'; ";
            $csp .= "frame-ancestors 'self'; ";
            $csp .= "object-src 'none'; ";
            $csp .= "base-uri 'self';";
            
            header("Content-Security-Policy: " . $csp);
        }
    }
    
    /**
     * Enqueue security scripts
     */
    public static function enqueue_security_scripts() {
        $video_page = get_query_var('video_page');
        
        if (in_array($video_page, array('stream', 'my_recordings'))) {
            wp_enqueue_script(
                'video-security',
                VIDEO_BOOKING_URL . 'assets/js/video-security.js',
                array('jquery'),
                VIDEO_BOOKING_VERSION,
                true
            );
            
            wp_localize_script('video-security', 'videoSecurity', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('video_security_nonce'),
                'user_id' => get_current_user_id(),
                'warnings' => array(
                    'devtools' => 'Developer tools detected. Video access may be restricted.',
                    'recording' => 'Screen recording detected. This violates our terms of service.',
                    'suspicious' => 'Suspicious activity detected. Please contact support if you need assistance.'
                )
            ));
        }
    }
    
    /**
     * Block direct access to video files
     */
    public static function block_direct_video_access() {
        // Check if someone is trying to access video files directly
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        
        if (strpos($request_uri, '/private-videos/') !== false) {
            // Block direct access
            status_header(403);
            exit('Direct access to video files is not allowed.');
        }
    }
    
    /**
     * Add activity monitoring
     */
    public static function add_activity_monitoring() {
        $video_page = get_query_var('video_page');
        
        if ($video_page === 'stream') {
            ?>
            <script>
            // Advanced security monitoring
            (function() {
                'use strict';
                
                let suspiciousActivity = 0;
                let devToolsOpen = false;
                let recordingDetected = false;
                
                // Detect developer tools
                function detectDevTools() {
                    const threshold = 160;
                    
                    if (window.outerHeight - window.innerHeight > threshold || 
                        window.outerWidth - window.innerWidth > threshold) {
                        if (!devToolsOpen) {
                            devToolsOpen = true;
                            logSuspiciousActivity('devtools_opened');
                        }
                    } else {
                        devToolsOpen = false;
                    }
                }
                
                // Detect screen recording (basic detection)
                function detectScreenRecording() {
                    // Check for common screen recording software indicators
                    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                        // Monitor for display capture
                        const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia;
                        navigator.mediaDevices.getDisplayMedia = function() {
                            recordingDetected = true;
                            logSuspiciousActivity('screen_recording_attempt');
                            return originalGetDisplayMedia.apply(this, arguments);
                        };
                    }
                    
                    // Check for unusual window focus patterns
                    let focusChanges = 0;
                    let lastFocusChange = Date.now();
                    
                    window.addEventListener('blur', function() {
                        focusChanges++;
                        const now = Date.now();
                        if (now - lastFocusChange < 1000 && focusChanges > 10) {
                            logSuspiciousActivity('rapid_focus_changes');
                        }
                        lastFocusChange = now;
                    });
                }
                
                // Log suspicious activity
                function logSuspiciousActivity(type) {
                    suspiciousActivity++;
                    
                    console.warn('Suspicious activity detected:', type);
                    
                    // Send to server for logging
                    if (typeof videoSecurity !== 'undefined') {
                        jQuery.ajax({
                            url: videoSecurity.ajax_url,
                            type: 'POST',
                            data: {
                                action: 'log_suspicious_activity',
                                nonce: videoSecurity.nonce,
                                activity_type: type,
                                user_id: videoSecurity.user_id,
                                timestamp: Date.now(),
                                user_agent: navigator.userAgent,
                                screen_resolution: screen.width + 'x' + screen.height,
                                window_size: window.innerWidth + 'x' + window.innerHeight
                            }
                        });
                    }
                    
                    // Show warning after multiple violations
                    if (suspiciousActivity >= 3) {
                        showSecurityWarning();
                    }
                }
                
                // Show security warning
                function showSecurityWarning() {
                    const warning = document.createElement('div');
                    warning.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        right: 0;
                        background: #dc3545;
                        color: white;
                        padding: 15px;
                        text-align: center;
                        z-index: 9999;
                        font-weight: bold;
                    `;
                    warning.innerHTML = 'Security Warning: Unauthorized activity detected. Video access may be restricted.';
                    document.body.appendChild(warning);
                    
                    setTimeout(() => {
                        if (warning.parentNode) {
                            warning.parentNode.removeChild(warning);
                        }
                    }, 10000);
                }
                
                // Disable common shortcuts
                document.addEventListener('keydown', function(e) {
                    // F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U, Ctrl+S, Ctrl+A, Ctrl+P
                    const blockedKeys = [
                        {key: 123}, // F12
                        {key: 73, ctrl: true, shift: true}, // Ctrl+Shift+I
                        {key: 74, ctrl: true, shift: true}, // Ctrl+Shift+J
                        {key: 85, ctrl: true}, // Ctrl+U
                        {key: 83, ctrl: true}, // Ctrl+S
                        {key: 65, ctrl: true}, // Ctrl+A
                        {key: 80, ctrl: true}, // Ctrl+P
                        {key: 67, ctrl: true}, // Ctrl+C
                        {key: 86, ctrl: true}, // Ctrl+V
                    ];
                    
                    for (let blocked of blockedKeys) {
                        if (e.keyCode === blocked.key &&
                            (!blocked.ctrl || e.ctrlKey) &&
                            (!blocked.shift || e.shiftKey)) {
                            e.preventDefault();
                            logSuspiciousActivity('blocked_shortcut_' + blocked.key);
                            return false;
                        }
                    }
                });
                
                // Disable right-click
                document.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    logSuspiciousActivity('right_click_attempt');
                    return false;
                });
                
                // Disable text selection
                document.addEventListener('selectstart', function(e) {
                    if (e.target.tagName === 'VIDEO' || e.target.closest('.video-player-container')) {
                        e.preventDefault();
                        return false;
                    }
                });
                
                // Disable drag and drop
                document.addEventListener('dragstart', function(e) {
                    if (e.target.tagName === 'VIDEO') {
                        e.preventDefault();
                        logSuspiciousActivity('video_drag_attempt');
                        return false;
                    }
                });
                
                // Monitor for print attempts
                window.addEventListener('beforeprint', function(e) {
                    e.preventDefault();
                    logSuspiciousActivity('print_attempt');
                    return false;
                });
                
                // Detect screenshot attempts (limited detection)
                document.addEventListener('keydown', function(e) {
                    // Print Screen key
                    if (e.keyCode === 44) {
                        logSuspiciousActivity('screenshot_attempt');
                    }
                    
                    // Windows + Shift + S (Windows screenshot tool)
                    if (e.keyCode === 83 && e.shiftKey && e.metaKey) {
                        logSuspiciousActivity('screenshot_tool_attempt');
                    }
                });
                
                // Initialize monitoring
                setInterval(detectDevTools, 1000);
                detectScreenRecording();
                
                // Monitor video element specifically
                const video = document.querySelector('video');
                if (video) {
                    // Prevent video download
                    video.addEventListener('loadstart', function() {
                        video.removeAttribute('download');
                        video.controlsList = 'nodownload nofullscreen noremoteplayback';
                    });
                    
                    // Monitor video events
                    video.addEventListener('loadedmetadata', function() {
                        console.log('Video loaded - monitoring active');
                    });
                    
                    // Detect if video is being captured
                    video.addEventListener('play', function() {
                        // Basic check for video capture
                        if (video.captureStream) {
                            const originalCaptureStream = video.captureStream;
                            video.captureStream = function() {
                                logSuspiciousActivity('video_capture_attempt');
                                return originalCaptureStream.apply(this, arguments);
                            };
                        }
                    });
                }
                
                // Blur video when window loses focus (anti-recording measure)
                let blurTimeout;
                window.addEventListener('blur', function() {
                    const videoContainer = document.querySelector('.video-player-container');
                    if (videoContainer) {
                        blurTimeout = setTimeout(() => {
                            videoContainer.style.filter = 'blur(10px)';
                        }, 2000);
                    }
                });
                
                window.addEventListener('focus', function() {
                    clearTimeout(blurTimeout);
                    const videoContainer = document.querySelector('.video-player-container');
                    if (videoContainer) {
                        videoContainer.style.filter = 'none';
                    }
                });
                
            })();
            </script>
            <?php
        }
    }
    
    /**
     * Validate video access token
     */
    public static function validate_access_token($token, $video_id, $user_id) {
        if (!$token || !$video_id || !$user_id) {
            return false;
        }
        
        // Decode token
        $decoded = base64_decode($token);
        $parts = explode('|', $decoded);
        
        if (count($parts) !== 4) {
            return false;
        }
        
        list($token_video_id, $token_user_id, $expiry, $hash) = $parts;
        
        // Validate token components
        if ($token_video_id != $video_id || $token_user_id != $user_id) {
            return false;
        }
        
        // Check expiry
        if (time() > $expiry) {
            return false;
        }
        
        // Verify hash
        $data = $token_video_id . '|' . $token_user_id . '|' . $expiry;
        $expected_hash = hash_hmac('sha256', $data, wp_salt('secure_auth'));
        
        return hash_equals($expected_hash, $hash);
    }
    
    /**
     * Generate secure access token
     */
    public static function generate_access_token($video_id, $user_id, $duration = 3600) {
        $expiry = time() + $duration;
        $data = $video_id . '|' . $user_id . '|' . $expiry;
        $hash = hash_hmac('sha256', $data, wp_salt('secure_auth'));
        
        return base64_encode($data . '|' . $hash);
    }
    
    /**
     * Log security event
     */
    public static function log_security_event($event_type, $details = array()) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'event_type' => $event_type,
            'user_id' => get_current_user_id(),
            'ip_address' => self::get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'details' => $details
        );
        
        // Log to WordPress error log
        error_log('Video Security Event: ' . json_encode($log_entry));
        
        // Store in database for analysis
        global $wpdb;
        $table = $wpdb->prefix . 'video_booking_security_log';
        
        // Create table if it doesn't exist
        $sql = "CREATE TABLE IF NOT EXISTS $table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            timestamp datetime NOT NULL,
            event_type varchar(50) NOT NULL,
            user_id bigint(20) unsigned,
            ip_address varchar(45),
            user_agent text,
            details longtext,
            PRIMARY KEY (id),
            KEY timestamp (timestamp),
            KEY event_type (event_type),
            KEY user_id (user_id)
        ) {$wpdb->get_charset_collate()};";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Insert log entry
        $wpdb->insert($table, array(
            'timestamp' => $log_entry['timestamp'],
            'event_type' => $log_entry['event_type'],
            'user_id' => $log_entry['user_id'],
            'ip_address' => $log_entry['ip_address'],
            'user_agent' => substr($log_entry['user_agent'], 0, 500),
            'details' => json_encode($log_entry['details'])
        ));
    }
    
    /**
     * Get client IP address
     */
    private static function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}

// Initialize security
VideoBookingSecurity::init();

// AJAX handler for logging suspicious activity
add_action('wp_ajax_log_suspicious_activity', 'log_suspicious_activity_ajax');
add_action('wp_ajax_nopriv_log_suspicious_activity', 'log_suspicious_activity_ajax');

function log_suspicious_activity_ajax() {
    check_ajax_referer('video_security_nonce', 'nonce');
    
    $activity_type = sanitize_text_field($_POST['activity_type']);
    $details = array(
        'timestamp' => absint($_POST['timestamp']),
        'user_agent' => sanitize_text_field($_POST['user_agent']),
        'screen_resolution' => sanitize_text_field($_POST['screen_resolution']),
        'window_size' => sanitize_text_field($_POST['window_size'])
    );
    
    VideoBookingSecurity::log_security_event($activity_type, $details);
    
    wp_send_json_success('Activity logged');
}
