<?php
/**
 * Email Functions
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Booking Email Functions Class
 */
class VideoBookingEmails {
    
    /**
     * Initialize email functions
     */
    public static function init() {
        // Hook into order completion
        add_action('video_booking_order_completed', array(__CLASS__, 'send_order_completion_emails'));
        
        // Hook into early bird video availability
        add_action('video_booking_early_bird_available', array(__CLASS__, 'send_early_bird_notification'));
        
        // Set up email templates directory
        self::ensure_email_templates_directory();
    }
    
    /**
     * Send order completion emails
     */
    public static function send_order_completion_emails($order_id) {
        $order = VideoBookingDB::get_order($order_id, true);
        if (!$order) {
            return false;
        }
        
        // Send customer email
        self::send_customer_purchase_email($order);
        
        // Send admin notification
        self::send_admin_purchase_notification($order);
        
        return true;
    }
    
    /**
     * Send customer purchase confirmation email
     */
    public static function send_customer_purchase_email($order) {
        $template_path = VIDEO_BOOKING_PATH . 'emails/purchase-customer.php';
        
        // Prepare email data
        $email_data = array(
            'order' => $order,
            'customer_name' => $order->customer_name,
            'order_number' => $order->order_number,
            'total_amount' => $order->total_amount,
            'my_recordings_url' => home_url('/my-account/my-recordings'),
            'site_name' => get_bloginfo('name'),
            'site_url' => home_url(),
            'support_email' => get_option('admin_email')
        );
        
        // Generate email content
        $subject = 'Order Confirmation - ' . $order->order_number;
        $message = self::render_email_template($template_path, $email_data);
        
        // Email headers
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
            'Reply-To: ' . get_option('admin_email')
        );
        
        return wp_mail($order->customer_email, $subject, $message, $headers);
    }
    
    /**
     * Send admin purchase notification
     */
    public static function send_admin_purchase_notification($order) {
        $template_path = VIDEO_BOOKING_PATH . 'emails/purchase-admin.php';
        
        // Prepare email data
        $email_data = array(
            'order' => $order,
            'customer_name' => $order->customer_name,
            'customer_email' => $order->customer_email,
            'order_number' => $order->order_number,
            'total_amount' => $order->total_amount,
            'admin_url' => admin_url('admin.php?page=video-booking-orders'),
            'site_name' => get_bloginfo('name')
        );
        
        // Generate email content
        $subject = 'New Video Purchase - ' . $order->order_number;
        $message = self::render_email_template($template_path, $email_data);
        
        // Email headers
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );
        
        return wp_mail(get_option('admin_email'), $subject, $message, $headers);
    }
    
    /**
     * Send early bird availability notification
     */
    public static function send_early_bird_notification($user_id, $video_id) {
        $user = get_user_by('id', $user_id);
        $video = VideoBookingDB::get_video($video_id);
        
        if (!$user || !$video) {
            return false;
        }
        
        $template_path = VIDEO_BOOKING_PATH . 'emails/earlybird-available.php';
        
        // Prepare email data
        $email_data = array(
            'user' => $user,
            'video' => $video,
            'customer_name' => $user->display_name,
            'video_title' => $video->title,
            'video_description' => $video->description,
            'my_recordings_url' => home_url('/my-account/my-recordings'),
            'duration_days' => $video->duration_days,
            'site_name' => get_bloginfo('name'),
            'site_url' => home_url(),
            'support_email' => get_option('admin_email')
        );
        
        // Generate email content
        $subject = 'Your Early Bird Video is Now Available!';
        $message = self::render_email_template($template_path, $email_data);
        
        // Email headers
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
            'Reply-To: ' . get_option('admin_email')
        );
        
        return wp_mail($user->user_email, $subject, $message, $headers);
    }
    
    /**
     * Render email template
     */
    private static function render_email_template($template_path, $data) {
        if (!file_exists($template_path)) {
            return self::get_fallback_email_content($data);
        }
        
        // Extract data variables
        extract($data);
        
        // Start output buffering
        ob_start();
        
        // Include template
        include $template_path;
        
        // Get content and clean buffer
        $content = ob_get_clean();
        
        return $content;
    }
    
    /**
     * Get fallback email content if template doesn't exist
     */
    private static function get_fallback_email_content($data) {
        $content = '<html><body>';
        $content .= '<h2>' . get_bloginfo('name') . '</h2>';
        
        if (isset($data['order'])) {
            $content .= '<h3>Order Confirmation</h3>';
            $content .= '<p>Thank you for your purchase!</p>';
            $content .= '<p>Order Number: ' . $data['order_number'] . '</p>';
            $content .= '<p>Total: ₹' . number_format($data['total_amount'], 2) . '</p>';
            $content .= '<p><a href="' . $data['my_recordings_url'] . '">Access Your Videos</a></p>';
        } elseif (isset($data['video'])) {
            $content .= '<h3>Video Available</h3>';
            $content .= '<p>Your early bird video is now available!</p>';
            $content .= '<p>Video: ' . $data['video_title'] . '</p>';
            $content .= '<p><a href="' . $data['my_recordings_url'] . '">Watch Now</a></p>';
        }
        
        $content .= '</body></html>';
        
        return $content;
    }
    
    /**
     * Ensure email templates directory exists
     */
    private static function ensure_email_templates_directory() {
        $emails_dir = VIDEO_BOOKING_PATH . 'emails/';
        
        if (!file_exists($emails_dir)) {
            wp_mkdir_p($emails_dir);
        }
        
        // Create default templates if they don't exist
        self::create_default_email_templates();
    }
    
    /**
     * Create default email templates
     */
    private static function create_default_email_templates() {
        $templates = array(
            'purchase-customer.php' => self::get_customer_purchase_template(),
            'purchase-admin.php' => self::get_admin_purchase_template(),
            'earlybird-available.php' => self::get_earlybird_template()
        );
        
        foreach ($templates as $filename => $content) {
            $file_path = VIDEO_BOOKING_PATH . 'emails/' . $filename;
            if (!file_exists($file_path)) {
                file_put_contents($file_path, $content);
            }
        }
    }
    
    /**
     * Get customer purchase email template
     */
    private static function get_customer_purchase_template() {
        return '<?php
/**
 * Customer Purchase Confirmation Email Template
 */
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; }
        .header { background: #2c5aa0; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .order-details { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .video-item { border-bottom: 1px solid #eee; padding: 10px 0; }
        .video-item:last-child { border-bottom: none; }
        .btn { background: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo $site_name; ?></h1>
            <h2>Order Confirmation</h2>
        </div>
        
        <div class="content">
            <p>Dear <?php echo esc_html($customer_name); ?>,</p>
            
            <p>Thank you for your purchase! Your order has been confirmed and your videos are now available.</p>
            
            <div class="order-details">
                <h3>Order Details</h3>
                <p><strong>Order Number:</strong> <?php echo esc_html($order_number); ?></p>
                <p><strong>Total Amount:</strong> ₹<?php echo number_format($total_amount, 2); ?></p>
                <p><strong>Order Date:</strong> <?php echo date("M j, Y g:i A", strtotime($order->created_at)); ?></p>
            </div>
            
            <h3>Your Videos</h3>
            <?php foreach ($order->items as $item): ?>
                <div class="video-item">
                    <strong><?php echo esc_html($item->title); ?></strong><br>
                    <span>Price: ₹<?php echo number_format($item->price, 2); ?></span>
                    <?php if ($item->is_early_bird): ?>
                        <span style="color: #ff6b35; font-weight: bold;"> (Early Bird)</span>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
            
            <p style="text-align: center;">
                <a href="<?php echo esc_url($my_recordings_url); ?>" class="btn">Access Your Videos</a>
            </p>
            
            <p><strong>Important Notes:</strong></p>
            <ul>
                <li>Your videos are available in the "My Recordings" section of your account</li>
                <li>Early bird videos will be available after the workshop is completed</li>
                <li>Each video has a limited access period as specified during purchase</li>
                <li>Videos are for personal use only and cannot be shared or downloaded</li>
            </ul>
            
            <p>If you have any questions or need assistance, please contact us at <a href="mailto:<?php echo $support_email; ?>"><?php echo $support_email; ?></a></p>
            
            <p>Thank you for choosing <?php echo $site_name; ?>!</p>
        </div>
        
        <div class="footer">
            <p>&copy; <?php echo date("Y"); ?> <?php echo $site_name; ?>. All rights reserved.</p>
            <p><a href="<?php echo $site_url; ?>"><?php echo $site_url; ?></a></p>
        </div>
    </div>
</body>
</html>';
    }
    
    /**
     * Get admin purchase notification template
     */
    private static function get_admin_purchase_template() {
        return '<?php
/**
 * Admin Purchase Notification Email Template
 */
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>New Video Purchase</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2c5aa0; color: white; padding: 15px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .order-info { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .btn { background: #2c5aa0; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>New Video Purchase</h2>
        </div>
        
        <div class="content">
            <p>A new video purchase has been made on <?php echo $site_name; ?>.</p>
            
            <div class="order-info">
                <h3>Order Information</h3>
                <p><strong>Order Number:</strong> <?php echo esc_html($order_number); ?></p>
                <p><strong>Customer:</strong> <?php echo esc_html($customer_name); ?></p>
                <p><strong>Email:</strong> <?php echo esc_html($customer_email); ?></p>
                <p><strong>Total Amount:</strong> ₹<?php echo number_format($total_amount, 2); ?></p>
                <p><strong>Order Date:</strong> <?php echo date("M j, Y g:i A", strtotime($order->created_at)); ?></p>
                
                <h4>Items Purchased:</h4>
                <?php foreach ($order->items as $item): ?>
                    <p>• <?php echo esc_html($item->title); ?> - ₹<?php echo number_format($item->price, 2); ?>
                    <?php if ($item->is_early_bird): ?>
                        <em>(Early Bird)</em>
                    <?php endif; ?>
                    </p>
                <?php endforeach; ?>
            </div>
            
            <p style="text-align: center;">
                <a href="<?php echo esc_url($admin_url); ?>" class="btn">View in Admin</a>
            </p>
        </div>
    </div>
</body>
</html>';
    }
    
    /**
     * Get early bird available email template
     */
    private static function get_earlybird_template() {
        return '<?php
/**
 * Early Bird Available Email Template
 */
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Early Bird Video is Available!</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background: white; }
        .header { background: linear-gradient(135deg, #ff6b35, #f39c12); color: white; padding: 30px 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .video-info { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff6b35; }
        .btn { background: #2c5aa0; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0; font-weight: bold; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
        .highlight { color: #ff6b35; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Great News!</h1>
            <h2>Your Early Bird Video is Ready</h2>
        </div>
        
        <div class="content">
            <p>Hi <?php echo esc_html($customer_name); ?>,</p>
            
            <p>We\'re excited to let you know that the workshop recording you purchased as an early bird is now available to watch!</p>
            
            <div class="video-info">
                <h3><?php echo esc_html($video_title); ?></h3>
                <?php if ($video_description): ?>
                    <p><?php echo wp_kses_post(wpautop($video_description)); ?></p>
                <?php endif; ?>
                <p><strong>Access Duration:</strong> <span class="highlight"><?php echo $duration_days; ?> days</span> from today</p>
            </div>
            
            <p style="text-align: center;">
                <a href="<?php echo esc_url($my_recordings_url); ?>" class="btn">Watch Now</a>
            </p>
            
            <p><strong>Important Reminders:</strong></p>
            <ul>
                <li>Your access period starts from today and lasts for <?php echo $duration_days; ?> days</li>
                <li>You can watch the video as many times as you want during this period</li>
                <li>The video is available on any device when you\'re logged into your account</li>
                <li>Videos are for personal use only and cannot be shared or downloaded</li>
            </ul>
            
            <p>Thank you for your early bird purchase and for being part of our learning community!</p>
            
            <p>If you have any questions or need assistance, please contact us at <a href="mailto:<?php echo $support_email; ?>"><?php echo $support_email; ?></a></p>
            
            <p>Happy learning!</p>
            <p>The <?php echo $site_name; ?> Team</p>
        </div>
        
        <div class="footer">
            <p>&copy; <?php echo date("Y"); ?> <?php echo $site_name; ?>. All rights reserved.</p>
            <p><a href="<?php echo $site_url; ?>"><?php echo $site_url; ?></a></p>
        </div>
    </div>
</body>
</html>';
    }
}

// Initialize email functions
VideoBookingEmails::init();
