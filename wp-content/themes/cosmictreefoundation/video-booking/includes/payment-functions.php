<?php

/**
 * Payment Functions
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Payment Functions Class
 */
class VideoBookingPayments
{

    /**
     * Create order and initiate payment
     */
    public static function create_order($customer_data)
    {
        // Validate cart
        $cart = VideoBookingCart::get_cart();
        if (empty($cart)) {
            return array(
                'success' => false,
                'message' => 'Cart is empty'
            );
        }

        $cart_errors = VideoBookingCart::validate_cart();
        if (!empty($cart_errors)) {
            return array(
                'success' => false,
                'message' => implode(', ', $cart_errors)
            );
        }

        // Calculate totals
        $subtotal = VideoBookingCart::get_cart_total();
        $applied_coupon = VideoBookingCoupons::get_applied_coupon();
        $discount = $applied_coupon ? $applied_coupon['discount_amount'] : 0;
        $total = $subtotal - $discount;

        // Create user if requested
        $user_id = 0;
        if (!empty($customer_data['create_account']) && !is_user_logged_in()) {
            $user_id = self::create_customer_account($customer_data);
        } elseif (is_user_logged_in()) {
            $user_id = get_current_user_id();
        }

        // Create order in database
        $order_data = array(
            'user_id' => $user_id,
            'total_amount' => $total,
            'coupon_code' => $applied_coupon ? $applied_coupon['code'] : '',
            'coupon_discount' => $discount,
            'customer_name' => sanitize_text_field($customer_data['customer_name']),
            'customer_email' => sanitize_email($customer_data['customer_email']),
            'customer_phone' => sanitize_text_field($customer_data['customer_phone']),
        );

        $order_id = VideoBookingDB::create_order($order_data);
        if (!$order_id) {
            return array(
                'success' => false,
                'message' => 'Failed to create order'
            );
        }

        // Add order items
        foreach ($cart as $item) {
            VideoBookingDB::add_order_item($order_id, $item['video_id'], $item['price'], $item['is_early_bird']);
        }

        // If total is 0 (free order), complete immediately
        if ($total == 0) {
            return self::complete_free_order($order_id);
        }

        // Create Razorpay order
        $razorpay_order = self::create_razorpay_order($order_id, $total, $customer_data);
        if (!$razorpay_order['success']) {
            return $razorpay_order;
        }

        // Update order with Razorpay order ID
        VideoBookingDB::update_order_status($order_id, 'pending', array(
            'razorpay_order_id' => $razorpay_order['order_id']
        ));

        return array(
            'success' => true,
            'order_id' => $order_id,
            'razorpay_order_id' => $razorpay_order['order_id'],
            'amount' => $total * 100, // Razorpay expects amount in paise
            'customer_name' => $customer_data['customer_name'],
            'customer_email' => $customer_data['customer_email'],
            'customer_phone' => $customer_data['customer_phone']
        );
    }

    /**
     * Create Razorpay order
     */
    private static function create_razorpay_order($order_id, $amount, $customer_data)
    {
        if (!defined('RAZORPAY_KEY') || !defined('RAZORPAY_SECRET')) {
            return array(
                'success' => false,
                'message' => 'Payment gateway not configured'
            );
        }

        try {
            // Use existing Razorpay integration
            require_once get_template_directory() . '/vendor/autoload.php';

            $api = new Razorpay\Api\Api(RAZORPAY_KEY, RAZORPAY_SECRET);

            $order_data = array(
                'receipt' => 'video_order_' . $order_id,
                'amount' => $amount * 100, // Amount in paise
                'currency' => 'INR',
                'notes' => array(
                    'order_id' => $order_id,
                    'customer_name' => $customer_data['customer_name'],
                    'customer_email' => $customer_data['customer_email'],
                    'type' => 'video_booking'
                )
            );

            $razorpay_order = $api->order->create($order_data);

            return array(
                'success' => true,
                'order_id' => $razorpay_order['id']
            );
        } catch (Exception $e) {
            error_log('Razorpay order creation failed: ' . $e->getMessage());
            return array(
                'success' => false,
                'message' => 'Payment gateway error: ' . $e->getMessage()
            );
        }
    }

    /**
     * Complete free order
     */
    private static function complete_free_order($order_id)
    {
        // Update order status
        VideoBookingDB::update_order_status($order_id, 'completed');

        // Grant video access
        self::grant_video_access($order_id);

        // Send confirmation emails
        self::send_order_emails($order_id);

        // Clear cart and coupon
        VideoBookingCart::clear_cart();

        // Increment coupon usage if applicable
        $applied_coupon = VideoBookingCoupons::get_applied_coupon();
        if ($applied_coupon) {
            VideoBookingCoupons::increment_usage($applied_coupon['code']);
        }

        return array(
            'success' => true,
            'is_free_order' => true,
            'redirect_url' => home_url('/my-recordings?order_completed=1')
        );
    }

    /**
     * Verify payment and complete order
     */
    public static function verify_payment($order_id, $payment_data)
    {
        if (!defined('RAZORPAY_KEY') || !defined('RAZORPAY_SECRET')) {
            return array(
                'success' => false,
                'message' => 'Payment gateway not configured'
            );
        }

        try {
            require_once get_template_directory() . '/vendor/autoload.php';

            $api = new Razorpay\Api\Api(RAZORPAY_KEY, RAZORPAY_SECRET);

            // Verify signature
            $attributes = array(
                'razorpay_order_id' => $payment_data['razorpay_order_id'],
                'razorpay_payment_id' => $payment_data['razorpay_payment_id'],
                'razorpay_signature' => $payment_data['razorpay_signature']
            );

            $api->utility->verifyPaymentSignature($attributes);

            // Update order status
            VideoBookingDB::update_order_status($order_id, 'completed', array(
                'razorpay_payment_id' => $payment_data['razorpay_payment_id'],
                'razorpay_signature' => $payment_data['razorpay_signature']
            ));

            // Grant video access
            self::grant_video_access($order_id);

            // Send confirmation emails
            self::send_order_emails($order_id);

            // Clear cart and increment coupon usage
            $applied_coupon = VideoBookingCoupons::get_applied_coupon();
            VideoBookingCart::clear_cart();

            if ($applied_coupon) {
                VideoBookingCoupons::increment_usage($applied_coupon['code']);
            }

            return array(
                'success' => true,
                'redirect_url' => home_url('/my-recordings?order_completed=1')
            );
        } catch (Exception $e) {
            error_log('Payment verification failed: ' . $e->getMessage());

            // Update order status to failed
            VideoBookingDB::update_order_status($order_id, 'failed');

            return array(
                'success' => false,
                'message' => 'Payment verification failed'
            );
        }
    }

    /**
     * Grant video access to user
     */
    private static function grant_video_access($order_id)
    {
        $order = VideoBookingDB::get_order($order_id);
        if (!$order || !$order->items) {
            return false;
        }

        foreach ($order->items as $item) {
            VideoBookingDB::grant_video_access(
                $order->user_id,
                $item->video_id,
                $order_id,
                $item->is_early_bird
            );
        }

        return true;
    }

    /**
     * Send order confirmation emails
     */
    private static function send_order_emails($order_id)
    {
        // This will be implemented in the email functions
        do_action('video_booking_order_completed', $order_id);
    }

    /**
     * Create customer account
     */
    private static function create_customer_account($customer_data)
    {
        $username = sanitize_user($customer_data['customer_email']);
        $email = sanitize_email($customer_data['customer_email']);
        $password = wp_generate_password();

        $user_id = wp_create_user($username, $password, $email);

        if (!is_wp_error($user_id)) {
            // Update user meta
            wp_update_user(array(
                'ID' => $user_id,
                'display_name' => $customer_data['customer_name'],
                'first_name' => $customer_data['customer_name']
            ));

            // Send welcome email with login details
            wp_new_user_notification($user_id, null, 'user');

            return $user_id;
        }

        return 0;
    }
}

// AJAX handlers for payment functionality
add_action('wp_ajax_video_create_order', 'video_create_order_ajax');
add_action('wp_ajax_nopriv_video_create_order', 'video_create_order_ajax');

function video_create_order_ajax()
{
    check_ajax_referer('video_booking_nonce', 'nonce');

    $customer_data = array(
        'customer_name' => sanitize_text_field($_POST['customer_name']),
        'customer_email' => sanitize_email($_POST['customer_email']),
        'customer_phone' => sanitize_text_field($_POST['customer_phone']),
        'customer_city' => sanitize_text_field($_POST['customer_city']),
        'create_account' => isset($_POST['create_account']) ? 1 : 0,
    );

    // Validation
    if (empty($customer_data['customer_name'])) {
        wp_send_json_error('Name is required');
    }

    if (empty($customer_data['customer_email']) || !is_email($customer_data['customer_email'])) {
        wp_send_json_error('Valid email is required');
    }

    if (!isset($_POST['agree_terms'])) {
        wp_send_json_error('You must agree to the terms and conditions');
    }

    $result = VideoBookingPayments::create_order($customer_data);

    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result['message']);
    }
}

add_action('wp_ajax_video_verify_payment', 'video_verify_payment_ajax');
add_action('wp_ajax_nopriv_video_verify_payment', 'video_verify_payment_ajax');

function video_verify_payment_ajax()
{
    check_ajax_referer('video_booking_nonce', 'nonce');

    $order_id = absint($_POST['order_id']);
    $payment_data = array(
        'razorpay_payment_id' => sanitize_text_field($_POST['razorpay_payment_id']),
        'razorpay_order_id' => sanitize_text_field($_POST['razorpay_order_id']),
        'razorpay_signature' => sanitize_text_field($_POST['razorpay_signature'])
    );

    $result = VideoBookingPayments::verify_payment($order_id, $payment_data);

    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result['message']);
    }
}
