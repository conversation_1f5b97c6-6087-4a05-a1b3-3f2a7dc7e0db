<?php
/**
 * Razorpay Webhook Handler for Video Booking
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Booking Webhook Handler Class
 */
class VideoBookingWebhookHandler {
    
    /**
     * Initialize webhook handler
     */
    public static function init() {
        // Add webhook endpoint
        add_action('wp_ajax_nopriv_video_razorpay_webhook', array(__CLASS__, 'handle_webhook'));
        add_action('wp_ajax_video_razorpay_webhook', array(__CLASS__, 'handle_webhook'));
        
        // Add rewrite rule for webhook endpoint
        add_action('init', array(__CLASS__, 'add_webhook_rewrite_rule'));
        
        // Handle webhook via template redirect
        add_action('template_redirect', array(__CLASS__, 'handle_webhook_request'));
    }
    
    /**
     * Add rewrite rule for webhook endpoint
     */
    public static function add_webhook_rewrite_rule() {
        add_rewrite_rule(
            '^video-webhook/razorpay/?$',
            'index.php?video_webhook=razorpay',
            'top'
        );
        
        add_rewrite_tag('%video_webhook%', '([^&]+)');
        
        // Flush rewrite rules if needed
        if (get_option('video_webhook_rewrite_flushed') !== VIDEO_BOOKING_VERSION) {
            flush_rewrite_rules();
            update_option('video_webhook_rewrite_flushed', VIDEO_BOOKING_VERSION);
        }
    }
    
    /**
     * Handle webhook request via template redirect
     */
    public static function handle_webhook_request() {
        $webhook_type = get_query_var('video_webhook');
        
        if ($webhook_type === 'razorpay') {
            self::handle_webhook();
        }
    }
    
    /**
     * Handle Razorpay webhook
     */
    public static function handle_webhook() {
        // Log webhook attempt
        error_log('Video booking webhook received: ' . $_SERVER['REQUEST_METHOD']);
        
        // Only accept POST requests
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            exit('Method not allowed');
        }
        
        // Get webhook payload
        $payload = file_get_contents('php://input');
        $headers = getallheaders();
        
        if (empty($payload)) {
            http_response_code(400);
            exit('Empty payload');
        }
        
        // Verify webhook signature
        if (!self::verify_webhook_signature($payload, $headers)) {
            error_log('Video booking webhook signature verification failed');
            http_response_code(401);
            exit('Unauthorized');
        }
        
        // Parse webhook data
        $webhook_data = json_decode($payload, true);
        
        if (!$webhook_data) {
            error_log('Video booking webhook invalid JSON payload');
            http_response_code(400);
            exit('Invalid JSON');
        }
        
        // Log webhook data
        error_log('Video booking webhook data: ' . json_encode($webhook_data));
        
        // Process webhook based on event type
        $result = self::process_webhook_event($webhook_data);
        
        if ($result) {
            http_response_code(200);
            echo json_encode(array('status' => 'success'));
        } else {
            http_response_code(500);
            echo json_encode(array('status' => 'error'));
        }
        
        exit;
    }
    
    /**
     * Verify webhook signature
     */
    private static function verify_webhook_signature($payload, $headers) {
        if (!defined('RAZORPAY_WEBHOOK_SECRET') || empty(RAZORPAY_WEBHOOK_SECRET)) {
            error_log('Razorpay webhook secret not configured');
            return false;
        }
        
        $signature = $headers['X-Razorpay-Signature'] ?? '';
        
        if (empty($signature)) {
            error_log('Missing Razorpay signature header');
            return false;
        }
        
        $expected_signature = hash_hmac('sha256', $payload, RAZORPAY_WEBHOOK_SECRET);
        
        return hash_equals($expected_signature, $signature);
    }
    
    /**
     * Process webhook event
     */
    private static function process_webhook_event($webhook_data) {
        $event = $webhook_data['event'] ?? '';
        $entity = $webhook_data['payload']['payment']['entity'] ?? array();
        
        switch ($event) {
            case 'payment.captured':
                return self::handle_payment_captured($entity);
                
            case 'payment.failed':
                return self::handle_payment_failed($entity);
                
            case 'payment.authorized':
                return self::handle_payment_authorized($entity);
                
            default:
                error_log('Unhandled webhook event: ' . $event);
                return true; // Return true to acknowledge receipt
        }
    }
    
    /**
     * Handle payment captured event
     */
    private static function handle_payment_captured($payment_entity) {
        $razorpay_payment_id = $payment_entity['id'] ?? '';
        $razorpay_order_id = $payment_entity['order_id'] ?? '';
        $amount = $payment_entity['amount'] ?? 0;
        $status = $payment_entity['status'] ?? '';
        
        if (empty($razorpay_order_id)) {
            error_log('Missing Razorpay order ID in webhook');
            return false;
        }
        
        // Find order by Razorpay order ID
        global $wpdb;
        $orders_table = $wpdb->prefix . 'video_booking_orders';
        
        $order = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $orders_table WHERE razorpay_order_id = %s",
            $razorpay_order_id
        ));
        
        if (!$order) {
            error_log('Order not found for Razorpay order ID: ' . $razorpay_order_id);
            return false;
        }
        
        // Check if already processed
        if ($order->payment_status === 'completed') {
            error_log('Order already completed: ' . $order->id);
            return true;
        }
        
        // Update order status
        $update_result = VideoBookingDB::update_order_status($order->id, 'completed', array(
            'razorpay_payment_id' => $razorpay_payment_id,
            'payment_id' => $razorpay_payment_id
        ));
        
        if (!$update_result) {
            error_log('Failed to update order status for order: ' . $order->id);
            return false;
        }
        
        // Grant video access
        self::grant_video_access_for_order($order->id);
        
        // Send confirmation emails
        do_action('video_booking_order_completed', $order->id);
        
        // Log successful processing
        error_log('Successfully processed payment captured for order: ' . $order->id);
        
        return true;
    }
    
    /**
     * Handle payment failed event
     */
    private static function handle_payment_failed($payment_entity) {
        $razorpay_order_id = $payment_entity['order_id'] ?? '';
        $error_code = $payment_entity['error_code'] ?? '';
        $error_description = $payment_entity['error_description'] ?? '';
        
        if (empty($razorpay_order_id)) {
            return false;
        }
        
        // Find order by Razorpay order ID
        global $wpdb;
        $orders_table = $wpdb->prefix . 'video_booking_orders';
        
        $order = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $orders_table WHERE razorpay_order_id = %s",
            $razorpay_order_id
        ));
        
        if (!$order) {
            return false;
        }
        
        // Update order status to failed
        VideoBookingDB::update_order_status($order->id, 'failed', array(
            'payment_error' => $error_code . ': ' . $error_description
        ));
        
        // Send failure notification email (optional)
        self::send_payment_failure_notification($order);
        
        error_log('Payment failed for order: ' . $order->id . ' - ' . $error_description);
        
        return true;
    }
    
    /**
     * Handle payment authorized event
     */
    private static function handle_payment_authorized($payment_entity) {
        $razorpay_order_id = $payment_entity['order_id'] ?? '';
        
        if (empty($razorpay_order_id)) {
            return false;
        }
        
        // Find order by Razorpay order ID
        global $wpdb;
        $orders_table = $wpdb->prefix . 'video_booking_orders';
        
        $order = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $orders_table WHERE razorpay_order_id = %s",
            $razorpay_order_id
        ));
        
        if (!$order) {
            return false;
        }
        
        // Update order status to authorized (waiting for capture)
        VideoBookingDB::update_order_status($order->id, 'authorized');
        
        error_log('Payment authorized for order: ' . $order->id);
        
        return true;
    }
    
    /**
     * Grant video access for completed order
     */
    private static function grant_video_access_for_order($order_id) {
        $order = VideoBookingDB::get_order($order_id, true);
        
        if (!$order || !$order->items) {
            return false;
        }
        
        foreach ($order->items as $item) {
            VideoBookingDB::grant_video_access(
                $order->user_id,
                $item->video_id,
                $order_id,
                $item->is_early_bird
            );
        }
        
        return true;
    }
    
    /**
     * Send payment failure notification
     */
    private static function send_payment_failure_notification($order) {
        $subject = 'Payment Failed - Order ' . $order->order_number;
        
        $message = "
        <h2>Payment Failed</h2>
        <p>Dear {$order->customer_name},</p>
        <p>Unfortunately, your payment for order {$order->order_number} could not be processed.</p>
        <p>Please try again or contact our support team for assistance.</p>
        <p><a href='" . home_url('/workshop-recordings') . "'>Try Again</a></p>
        <p>Best regards,<br>" . get_bloginfo('name') . " Team</p>
        ";
        
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );
        
        wp_mail($order->customer_email, $subject, $message, $headers);
    }
    
    /**
     * Get webhook endpoint URL
     */
    public static function get_webhook_url() {
        return home_url('/video-webhook/razorpay');
    }
    
    /**
     * Test webhook connectivity
     */
    public static function test_webhook() {
        $webhook_url = self::get_webhook_url();
        
        $test_payload = json_encode(array(
            'event' => 'test.event',
            'payload' => array(
                'test' => true,
                'timestamp' => time()
            )
        ));
        
        $response = wp_remote_post($webhook_url, array(
            'body' => $test_payload,
            'headers' => array(
                'Content-Type' => 'application/json',
                'X-Razorpay-Signature' => hash_hmac('sha256', $test_payload, RAZORPAY_WEBHOOK_SECRET ?? 'test')
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        return array(
            'success' => $response_code === 200,
            'response_code' => $response_code,
            'webhook_url' => $webhook_url
        );
    }
}

// Initialize webhook handler
VideoBookingWebhookHandler::init();

// Add admin notice for webhook configuration
add_action('admin_notices', function() {
    if (isset($_GET['page']) && strpos($_GET['page'], 'video-booking') !== false) {
        if (!defined('RAZORPAY_WEBHOOK_SECRET') || empty(RAZORPAY_WEBHOOK_SECRET)) {
            echo '<div class="notice notice-warning"><p>';
            echo '<strong>Video Booking:</strong> Razorpay webhook secret is not configured. ';
            echo 'Add <code>define("RAZORPAY_WEBHOOK_SECRET", "your_webhook_secret");</code> to wp-config.php for webhook processing.';
            echo '</p></div>';
        }
    }
});

// Add webhook URL to admin
add_action('admin_footer', function() {
    if (isset($_GET['page']) && $_GET['page'] === 'video-booking') {
        $webhook_url = VideoBookingWebhookHandler::get_webhook_url();
        echo '<script>console.log("Video Booking Webhook URL: ' . $webhook_url . '");</script>';
    }
});
