<?php
/**
 * Coupon Functions
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Coupon Functions Class
 */
class VideoBookingCoupons {
    
    /**
     * Validate coupon code
     */
    public static function validate_coupon($code, $cart_total = 0) {
        global $wpdb;
        
        if (empty($code)) {
            return array(
                'valid' => false,
                'message' => 'Please enter a coupon code'
            );
        }
        
        $coupons_table = $wpdb->prefix . 'video_booking_coupons';
        $coupon = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $coupons_table WHERE code = %s AND is_active = 1",
            strtoupper($code)
        ));
        
        if (!$coupon) {
            return array(
                'valid' => false,
                'message' => 'Invalid coupon code'
            );
        }
        
        // Check expiry
        if ($coupon->expires_at && strtotime($coupon->expires_at) < current_time('timestamp')) {
            return array(
                'valid' => false,
                'message' => 'This coupon has expired'
            );
        }
        
        // Check usage limit
        if ($coupon->usage_limit && $coupon->used_count >= $coupon->usage_limit) {
            return array(
                'valid' => false,
                'message' => 'This coupon has reached its usage limit'
            );
        }
        
        // Check minimum amount
        if ($coupon->minimum_amount > 0 && $cart_total < $coupon->minimum_amount) {
            return array(
                'valid' => false,
                'message' => 'Minimum order amount of ₹' . number_format($coupon->minimum_amount, 2) . ' required'
            );
        }
        
        return array(
            'valid' => true,
            'coupon' => $coupon,
            'message' => 'Coupon applied successfully'
        );
    }
    
    /**
     * Calculate discount amount
     */
    public static function calculate_discount($coupon, $cart_total) {
        if (!$coupon) {
            return 0;
        }
        
        if ($coupon->discount_type === 'percentage') {
            $discount = ($cart_total * $coupon->discount_value) / 100;
        } else {
            $discount = $coupon->discount_value;
        }
        
        // Ensure discount doesn't exceed cart total
        return min($discount, $cart_total);
    }
    
    /**
     * Apply coupon to cart
     */
    public static function apply_coupon($code, $cart_total) {
        $validation = self::validate_coupon($code, $cart_total);
        
        if (!$validation['valid']) {
            return $validation;
        }
        
        $coupon = $validation['coupon'];
        $discount = self::calculate_discount($coupon, $cart_total);
        
        // Store in session
        if (!session_id()) {
            session_start();
        }
        
        $_SESSION['video_coupon'] = array(
            'code' => $coupon->code,
            'discount_type' => $coupon->discount_type,
            'discount_value' => $coupon->discount_value,
            'discount_amount' => $discount
        );
        
        return array(
            'valid' => true,
            'coupon' => $coupon,
            'discount' => $discount,
            'message' => 'Coupon applied successfully'
        );
    }
    
    /**
     * Remove coupon from cart
     */
    public static function remove_coupon() {
        if (!session_id()) {
            session_start();
        }
        
        unset($_SESSION['video_coupon']);
    }
    
    /**
     * Get applied coupon from session
     */
    public static function get_applied_coupon() {
        if (!session_id()) {
            session_start();
        }
        
        return isset($_SESSION['video_coupon']) ? $_SESSION['video_coupon'] : null;
    }
    
    /**
     * Increment coupon usage count
     */
    public static function increment_usage($code) {
        global $wpdb;
        
        $coupons_table = $wpdb->prefix . 'video_booking_coupons';
        
        return $wpdb->query($wpdb->prepare(
            "UPDATE $coupons_table SET used_count = used_count + 1 WHERE code = %s",
            strtoupper($code)
        ));
    }
    
    /**
     * Get coupon by code
     */
    public static function get_coupon($code) {
        global $wpdb;
        
        $coupons_table = $wpdb->prefix . 'video_booking_coupons';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $coupons_table WHERE code = %s",
            strtoupper($code)
        ));
    }
    
    /**
     * Format discount display
     */
    public static function format_discount($coupon) {
        if (!$coupon) {
            return '';
        }
        
        if ($coupon->discount_type === 'percentage') {
            return $coupon->discount_value . '% off';
        } else {
            return '₹' . number_format($coupon->discount_value, 2) . ' off';
        }
    }
    
    /**
     * Check if coupon is valid for current time
     */
    public static function is_coupon_active($coupon) {
        if (!$coupon || !$coupon->is_active) {
            return false;
        }
        
        // Check expiry
        if ($coupon->expires_at && strtotime($coupon->expires_at) < current_time('timestamp')) {
            return false;
        }
        
        // Check usage limit
        if ($coupon->usage_limit && $coupon->used_count >= $coupon->usage_limit) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get all active coupons
     */
    public static function get_active_coupons() {
        global $wpdb;
        
        $coupons_table = $wpdb->prefix . 'video_booking_coupons';
        $current_time = current_time('mysql');
        
        $sql = "SELECT * FROM $coupons_table 
                WHERE is_active = 1 
                AND (expires_at IS NULL OR expires_at > %s)
                AND (usage_limit IS NULL OR used_count < usage_limit)
                ORDER BY created_at DESC";
        
        return $wpdb->get_results($wpdb->prepare($sql, $current_time));
    }
    
    /**
     * Clear expired coupons (cleanup function)
     */
    public static function cleanup_expired_coupons() {
        global $wpdb;
        
        $coupons_table = $wpdb->prefix . 'video_booking_coupons';
        $current_time = current_time('mysql');
        
        // Deactivate expired coupons
        return $wpdb->query($wpdb->prepare(
            "UPDATE $coupons_table SET is_active = 0 WHERE expires_at IS NOT NULL AND expires_at <= %s",
            $current_time
        ));
    }
}

// AJAX handlers for coupon functionality
add_action('wp_ajax_video_apply_coupon', 'video_apply_coupon_ajax');
add_action('wp_ajax_nopriv_video_apply_coupon', 'video_apply_coupon_ajax');

function video_apply_coupon_ajax() {
    check_ajax_referer('video_booking_nonce', 'nonce');
    
    $coupon_code = sanitize_text_field($_POST['coupon_code']);
    $cart_total = floatval($_POST['cart_total']);
    
    $result = VideoBookingCoupons::apply_coupon($coupon_code, $cart_total);
    
    if ($result['valid']) {
        wp_send_json_success(array(
            'message' => $result['message'],
            'discount' => $result['discount'],
            'coupon' => $result['coupon']
        ));
    } else {
        wp_send_json_error($result['message']);
    }
}

add_action('wp_ajax_video_remove_coupon', 'video_remove_coupon_ajax');
add_action('wp_ajax_nopriv_video_remove_coupon', 'video_remove_coupon_ajax');

function video_remove_coupon_ajax() {
    check_ajax_referer('video_booking_nonce', 'nonce');
    
    VideoBookingCoupons::remove_coupon();
    
    wp_send_json_success('Coupon removed');
}

// Schedule cleanup of expired coupons
add_action('video_booking_cleanup_coupons', array('VideoBookingCoupons', 'cleanup_expired_coupons'));

if (!wp_next_scheduled('video_booking_cleanup_coupons')) {
    wp_schedule_event(time(), 'daily', 'video_booking_cleanup_coupons');
}
