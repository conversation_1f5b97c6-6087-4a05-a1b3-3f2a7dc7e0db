<?php

/**
 * Coupon Admin Interface
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Coupon Admin Class
 */
class VideoCouponAdmin
{

    public function __construct()
    {
        add_action('admin_menu', array($this, 'add_coupon_menu'));
        add_action('wp_ajax_coupon_save', array($this, 'save_coupon'));
        add_action('wp_ajax_coupon_delete', array($this, 'delete_coupon'));
        add_action('wp_ajax_coupon_toggle_status', array($this, 'toggle_coupon_status'));
    }

    /**
     * Add coupon submenu
     */
    public function add_coupon_menu()
    {
        add_submenu_page(
            'video-booking',
            'Coupons',
            'Coupons',
            'manage_options',
            'video-booking-coupons',
            array($this, 'coupons_page')
        );

        add_submenu_page(
            'video-booking',
            'Add New Coupon',
            'Add New Coupon',
            'manage_options',
            'video-booking-add-coupon',
            array($this, 'add_coupon_page')
        );
    }

    /**
     * Coupons listing page
     */
    public function coupons_page()
    {
        global $wpdb;

        $coupons_table = $wpdb->prefix . 'video_booking_coupons';
        $coupons = $wpdb->get_results("SELECT * FROM $coupons_table ORDER BY created_at DESC");

?>
        <div class="wrap">
            <h1 class="wp-heading-inline">Coupon Codes</h1>
            <a href="<?php echo admin_url('admin.php?page=video-booking-add-coupon'); ?>" class="page-title-action">Add New Coupon</a>
            <hr class="wp-header-end">

            <div class="coupon-admin-container">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Code</th>
                            <th>Description</th>
                            <th>Discount</th>
                            <th>Usage</th>
                            <th>Expires</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($coupons)): ?>
                            <tr>
                                <td colspan="7" style="text-align: center; padding: 40px;">
                                    <p>No coupons found. <a href="<?php echo admin_url('admin.php?page=video-booking-add-coupon'); ?>">Create your first coupon</a></p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($coupons as $coupon): ?>
                                <tr data-coupon-id="<?php echo $coupon->id; ?>">
                                    <td><strong><?php echo esc_html($coupon->code); ?></strong></td>
                                    <td><?php echo esc_html($coupon->description); ?></td>
                                    <td>
                                        <?php if ($coupon->discount_type === 'percentage'): ?>
                                            <?php echo $coupon->discount_value; ?>%
                                        <?php else: ?>
                                            ₹<?php echo number_format($coupon->discount_value, 2); ?>
                                        <?php endif; ?>
                                        <?php if ($coupon->minimum_amount > 0): ?>
                                            <br><small>Min: ₹<?php echo number_format($coupon->minimum_amount, 2); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo $coupon->used_count; ?>
                                        <?php if ($coupon->usage_limit): ?>
                                            / <?php echo $coupon->usage_limit; ?>
                                        <?php else: ?>
                                            / ∞
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($coupon->expires_at): ?>
                                            <?php
                                            $expires = strtotime($coupon->expires_at);
                                            $is_expired = $expires < current_time('timestamp');
                                            ?>
                                            <span class="<?php echo $is_expired ? 'expired' : ''; ?>">
                                                <?php echo date('M j, Y', $expires); ?>
                                            </span>
                                        <?php else: ?>
                                            Never
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $coupon->is_active ? 'active' : 'inactive'; ?>">
                                            <?php echo $coupon->is_active ? 'Active' : 'Inactive'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?php echo admin_url('admin.php?page=video-booking-add-coupon&edit=' . $coupon->id); ?>" class="button button-small">Edit</a>
                                        <button class="button button-small toggle-coupon-status" data-coupon-id="<?php echo $coupon->id; ?>">
                                            <?php echo $coupon->is_active ? 'Deactivate' : 'Activate'; ?>
                                        </button>
                                        <button class="button button-small button-link-delete delete-coupon" data-coupon-id="<?php echo $coupon->id; ?>">Delete</button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <style>
            .expired {
                color: #d63638;
                font-weight: bold;
            }

            .coupon-admin-container {
                margin-top: 20px;
            }
        </style>

        <script>
            jQuery(document).ready(function($) {
                // Delete coupon
                $('.delete-coupon').click(function(e) {
                    e.preventDefault();

                    const $button = $(this);
                    const couponId = $button.data('coupon-id');

                    if (!confirm('Are you sure you want to delete this coupon? This action cannot be undone.')) {
                        return;
                    }

                    $button.prop('disabled', true).text('Deleting...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'coupon_delete',
                            nonce: '<?php echo wp_create_nonce('coupon_admin_nonce'); ?>',
                            coupon_id: couponId
                        },
                        success: function(response) {
                            if (response.success) {
                                $button.closest('tr').fadeOut(300, function() {
                                    $(this).remove();
                                });
                            } else {
                                alert(response.data || 'Failed to delete coupon');
                                $button.prop('disabled', false).text('Delete');
                            }
                        },
                        error: function() {
                            alert('Network error. Please try again.');
                            $button.prop('disabled', false).text('Delete');
                        }
                    });
                });

                // Toggle coupon status
                $('.toggle-coupon-status').click(function(e) {
                    e.preventDefault();

                    const $button = $(this);
                    const couponId = $button.data('coupon-id');
                    const $row = $button.closest('tr');

                    $button.prop('disabled', true);

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'coupon_toggle_status',
                            nonce: '<?php echo wp_create_nonce('coupon_admin_nonce'); ?>',
                            coupon_id: couponId
                        },
                        success: function(response) {
                            if (response.success) {
                                const newStatus = response.data.is_active;
                                const $statusBadge = $row.find('.status-badge');

                                if (newStatus) {
                                    $statusBadge.removeClass('status-inactive').addClass('status-active').text('Active');
                                    $button.text('Deactivate');
                                } else {
                                    $statusBadge.removeClass('status-active').addClass('status-inactive').text('Inactive');
                                    $button.text('Activate');
                                }
                            } else {
                                alert(response.data || 'Failed to update status');
                            }

                            $button.prop('disabled', false);
                        },
                        error: function() {
                            alert('Network error. Please try again.');
                            $button.prop('disabled', false);
                        }
                    });
                });
            });
        </script>
    <?php
    }

    /**
     * Add/Edit coupon page
     */
    public function add_coupon_page()
    {
        $coupon = null;
        $is_edit = false;

        if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
            global $wpdb;
            $coupons_table = $wpdb->prefix . 'video_booking_coupons';
            $coupon = $wpdb->get_row($wpdb->prepare("SELECT * FROM $coupons_table WHERE id = %d", $_GET['edit']));
            $is_edit = true;
        }

    ?>
        <div class="wrap">
            <h1><?php echo $is_edit ? 'Edit Coupon' : 'Add New Coupon'; ?></h1>
            <hr class="wp-header-end">

            <form id="coupon-form" class="coupon-form">
                <?php wp_nonce_field('coupon_admin_nonce', 'nonce'); ?>
                <?php if ($is_edit): ?>
                    <input type="hidden" name="coupon_id" value="<?php echo $coupon->id; ?>">
                <?php endif; ?>

                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="code">Coupon Code *</label></th>
                        <td>
                            <input type="text" id="code" name="code" class="regular-text" value="<?php echo $coupon ? esc_attr($coupon->code) : ''; ?>" required style="text-transform: uppercase;">
                            <p class="description">Enter a unique coupon code (letters and numbers only)</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="description">Description</label></th>
                        <td>
                            <textarea id="description" name="description" rows="3" class="large-text"><?php echo $coupon ? esc_textarea($coupon->description) : ''; ?></textarea>
                            <p class="description">Internal description for this coupon</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="discount_type">Discount Type *</label></th>
                        <td>
                            <select id="discount_type" name="discount_type" required>
                                <option value="flat" <?php echo (!$coupon || $coupon->discount_type === 'flat') ? 'selected' : ''; ?>>Flat Amount (₹)</option>
                                <option value="percentage" <?php echo ($coupon && $coupon->discount_type === 'percentage') ? 'selected' : ''; ?>>Percentage (%)</option>
                            </select>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="discount_value">Discount Value *</label></th>
                        <td>
                            <input type="number" id="discount_value" name="discount_value" step="0.01" min="0" class="small-text" value="<?php echo $coupon ? $coupon->discount_value : ''; ?>" required>
                            <span id="discount_unit">₹</span>
                            <p class="description">Amount or percentage to discount</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="minimum_amount">Minimum Order Amount</label></th>
                        <td>
                            <input type="number" id="minimum_amount" name="minimum_amount" step="0.01" min="0" class="small-text" value="<?php echo $coupon ? $coupon->minimum_amount : '0'; ?>">
                            <p class="description">Minimum cart amount required to use this coupon (0 for no minimum)</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="usage_limit">Usage Limit</label></th>
                        <td>
                            <input type="number" id="usage_limit" name="usage_limit" min="0" class="small-text" value="<?php echo $coupon ? $coupon->usage_limit : ''; ?>">
                            <p class="description">Maximum number of times this coupon can be used (leave empty for unlimited)</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="expires_at">Expiry Date</label></th>
                        <td>
                            <input type="datetime-local" id="expires_at" name="expires_at" value="<?php echo $coupon && $coupon->expires_at ? date('Y-m-d\TH:i', strtotime($coupon->expires_at)) : ''; ?>">
                            <p class="description">When this coupon expires (leave empty for no expiry)</p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><label for="is_active">Status</label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="is_active" name="is_active" value="1" <?php echo (!$coupon || $coupon->is_active) ? 'checked' : ''; ?>>
                                Make coupon active
                            </label>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <button type="submit" class="button button-primary"><?php echo $is_edit ? 'Update Coupon' : 'Add Coupon'; ?></button>
                    <a href="<?php echo admin_url('admin.php?page=video-booking-coupons'); ?>" class="button">Cancel</a>
                </p>
            </form>
        </div>

        <script>
            jQuery(document).ready(function($) {
                // Update discount unit based on type
                $('#discount_type').change(function() {
                    const type = $(this).val();
                    $('#discount_unit').text(type === 'percentage' ? '%' : '₹');
                }).trigger('change');

                // Convert code to uppercase
                $('#code').on('input', function() {
                    $(this).val($(this).val().toUpperCase().replace(/[^A-Z0-9]/g, ''));
                });

                // Form submission
                $('#coupon-form').submit(function(e) {
                    e.preventDefault();

                    const $form = $(this);
                    const $submitButton = $form.find('button[type="submit"]');
                    const originalText = $submitButton.text();

                    $submitButton.prop('disabled', true).text('Saving...');

                    const formData = $form.serialize();
                    formData += '&action=coupon_save';

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: formData,
                        success: function(response) {
                            if (response.success) {
                                alert('Coupon saved successfully!');
                                window.location.href = 'admin.php?page=video-booking-coupons';
                            } else {
                                alert(response.data || 'Failed to save coupon');
                                $submitButton.prop('disabled', false).text(originalText);
                            }
                        },
                        error: function() {
                            alert('Network error. Please try again.');
                            $submitButton.prop('disabled', false).text(originalText);
                        }
                    });
                });
            });
        </script>
<?php
    }

    /**
     * AJAX: Save coupon
     */
    public function save_coupon()
    {
        check_ajax_referer('coupon_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        global $wpdb;
        $coupons_table = $wpdb->prefix . 'video_booking_coupons';

        $coupon_id = isset($_POST['coupon_id']) ? absint($_POST['coupon_id']) : 0;
        $is_edit = $coupon_id > 0;

        $data = array(
            'code' => strtoupper(sanitize_text_field($_POST['code'])),
            'description' => sanitize_textarea_field($_POST['description']),
            'discount_type' => sanitize_text_field($_POST['discount_type']),
            'discount_value' => floatval($_POST['discount_value']),
            'minimum_amount' => floatval($_POST['minimum_amount']),
            'usage_limit' => !empty($_POST['usage_limit']) ? absint($_POST['usage_limit']) : null,
            'expires_at' => !empty($_POST['expires_at']) ? sanitize_text_field($_POST['expires_at']) : null,
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
        );

        // Validation
        if (empty($data['code'])) {
            wp_send_json_error('Coupon code is required');
        }

        if (!preg_match('/^[A-Z0-9]+$/', $data['code'])) {
            wp_send_json_error('Coupon code can only contain letters and numbers');
        }

        if (!in_array($data['discount_type'], array('flat', 'percentage'))) {
            wp_send_json_error('Invalid discount type');
        }

        if ($data['discount_value'] <= 0) {
            wp_send_json_error('Discount value must be greater than 0');
        }

        if ($data['discount_type'] === 'percentage' && $data['discount_value'] > 100) {
            wp_send_json_error('Percentage discount cannot exceed 100%');
        }

        // Check for duplicate code
        $existing_query = "SELECT id FROM $coupons_table WHERE code = %s";
        if ($is_edit) {
            $existing_query .= " AND id != %d";
            $existing = $wpdb->get_var($wpdb->prepare($existing_query, $data['code'], $coupon_id));
        } else {
            $existing = $wpdb->get_var($wpdb->prepare($existing_query, $data['code']));
        }

        if ($existing) {
            wp_send_json_error('Coupon code already exists');
        }

        if ($is_edit) {
            $result = $wpdb->update($coupons_table, $data, array('id' => $coupon_id));
            $message = 'Coupon updated successfully';
        } else {
            $result = $wpdb->insert($coupons_table, $data);
            $message = 'Coupon created successfully';
        }

        if ($result !== false) {
            wp_send_json_success($message);
        } else {
            wp_send_json_error('Failed to save coupon');
        }
    }

    /**
     * AJAX: Delete coupon
     */
    public function delete_coupon()
    {
        check_ajax_referer('coupon_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        global $wpdb;
        $coupons_table = $wpdb->prefix . 'video_booking_coupons';

        $coupon_id = absint($_POST['coupon_id']);

        if (!$coupon_id) {
            wp_send_json_error('Invalid coupon ID');
        }

        $result = $wpdb->delete($coupons_table, array('id' => $coupon_id), array('%d'));

        if ($result) {
            wp_send_json_success('Coupon deleted successfully');
        } else {
            wp_send_json_error('Failed to delete coupon');
        }
    }

    /**
     * AJAX: Toggle coupon status
     */
    public function toggle_coupon_status()
    {
        check_ajax_referer('coupon_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        global $wpdb;
        $coupons_table = $wpdb->prefix . 'video_booking_coupons';

        $coupon_id = absint($_POST['coupon_id']);

        if (!$coupon_id) {
            wp_send_json_error('Invalid coupon ID');
        }

        $coupon = $wpdb->get_row($wpdb->prepare("SELECT * FROM $coupons_table WHERE id = %d", $coupon_id));
        if (!$coupon) {
            wp_send_json_error('Coupon not found');
        }

        $new_status = $coupon->is_active ? 0 : 1;
        $result = $wpdb->update($coupons_table, array('is_active' => $new_status), array('id' => $coupon_id));

        if ($result !== false) {
            wp_send_json_success(array('is_active' => $new_status));
        } else {
            wp_send_json_error('Failed to update status');
        }
    }
}

// Initialize coupon admin
new VideoCouponAdmin();
