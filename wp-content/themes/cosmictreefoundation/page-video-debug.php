<?php

/**
 * Template Name: Video Debug
 * 
 * @package CosmicTreeFoundation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Enqueue video booking scripts
wp_enqueue_style('video-booking-style', get_template_directory_uri() . '/video-booking/assets/css/video-booking.css', array(), '1.0.0');
wp_enqueue_script('video-booking-js', get_template_directory_uri() . '/video-booking/assets/js/video-booking.js', array('jquery'), '1.0.0', true);

// Localize script with AJAX data
wp_localize_script('video-booking-js', 'videoBooking', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('video_booking_nonce'),
    'site_url' => home_url(),
    'user_id' => get_current_user_id(),
    'is_logged_in' => is_user_logged_in(),
    'razorpay_key' => defined('RAZORPAY_KEY') ? RAZORPAY_KEY : '',
    'currency' => 'INR',
    'messages' => array(
        'login_required' => 'Please log in to access this feature',
        'cart_empty' => 'Your cart is empty',
        'processing' => 'Processing...',
        'error' => 'An error occurred. Please try again.',
        'success' => 'Success!'
    )
));

get_header();
?>

<div class="container">
    <h1>Video Booking Debug</h1>

    <div class="debug-section">
        <h2>JavaScript Variables</h2>
        <div id="js-debug"></div>
    </div>

    <div class="debug-section">
        <h2>AJAX Test</h2>
        <button id="test-ajax" class="btn btn-primary">Test AJAX Connection</button>
        <div id="ajax-result"></div>
    </div>

    <div class="debug-section">
        <h2>Cart Test</h2>
        <button id="test-cart" class="btn btn-secondary">Test Cart Functions</button>
        <div id="cart-result"></div>
    </div>

    <div class="debug-section">
        <h2>Database Tables</h2>
        <?php
        global $wpdb;
        $tables = array(
            'video_booking_videos',
            'video_booking_orders',
            'video_booking_order_items',
            'video_booking_user_access',
            'video_booking_coupons'
        );

        foreach ($tables as $table) {
            $full_table = $wpdb->prefix . $table;
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$full_table'") == $full_table;
            echo "<p><strong>$table:</strong> " . ($exists ? '✅ Exists' : '❌ Missing') . "</p>";

            if ($exists) {
                $count = $wpdb->get_var("SELECT COUNT(*) FROM $full_table");
                echo "<p>&nbsp;&nbsp;&nbsp;Records: $count</p>";
            }
        }
        ?>
    </div>

    <div class="debug-section">
        <h2>Configuration</h2>
        <p><strong>Razorpay Key:</strong> <?php echo defined('RAZORPAY_KEY') ? (RAZORPAY_KEY ? '✅ Set' : '❌ Empty') : '❌ Not defined'; ?></p>
        <p><strong>Razorpay Secret:</strong> <?php echo defined('RAZORPAY_SECRET') ? (RAZORPAY_SECRET ? '✅ Set' : '❌ Empty') : '❌ Not defined'; ?></p>
        <p><strong>Session Started:</strong> <?php echo session_id() ? '✅ Yes' : '❌ No'; ?></p>
    </div>
</div>

<script>
    jQuery(document).ready(function($) {
        // Display JavaScript variables
        if (typeof videoBooking !== 'undefined') {
            $('#js-debug').html('<pre>' + JSON.stringify(videoBooking, null, 2) + '</pre>');
        } else {
            $('#js-debug').html('<p style="color: red;">videoBooking variable not defined</p>');
        }

        // Test AJAX connection
        $('#test-ajax').click(function() {
            if (typeof videoBooking === 'undefined') {
                $('#ajax-result').html('<p style="color: red;">videoBooking not defined</p>');
                return;
            }

            $.ajax({
                url: videoBooking.ajax_url,
                type: 'POST',
                data: {
                    action: 'video_get_cart',
                    nonce: videoBooking.nonce
                },
                success: function(response) {
                    $('#ajax-result').html('<p style="color: green;">AJAX Success: ' + JSON.stringify(response) + '</p>');
                },
                error: function(xhr, status, error) {
                    $('#ajax-result').html('<p style="color: red;">AJAX Error: ' + error + '</p>');
                }
            });
        });

        // Test cart functions
        $('#test-cart').click(function() {
            if (typeof VideoBooking !== 'undefined') {
                $('#cart-result').html('<p style="color: green;">VideoBooking object exists</p>');
            } else {
                $('#cart-result').html('<p style="color: red;">VideoBooking object not defined</p>');
            }
        });
    });
</script>

<style>
    .debug-section {
        margin: 20px 0;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }

    .debug-section h2 {
        margin-top: 0;
        color: #333;
    }

    pre {
        background: #f5f5f5;
        padding: 10px;
        border-radius: 3px;
        overflow-x: auto;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        margin: 5px;
    }

    .btn-primary {
        background: #007cba;
        color: white;
    }

    .btn-secondary {
        background: #666;
        color: white;
    }
</style>

<?php get_footer(); ?>