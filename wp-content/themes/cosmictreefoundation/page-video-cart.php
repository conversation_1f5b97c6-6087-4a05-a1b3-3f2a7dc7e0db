<?php

/**
 * Template Name: Video Cart
 *
 * @package CosmicTreeFoundation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Enqueue video booking scripts
wp_enqueue_style('video-booking-style', get_template_directory_uri() . '/video-booking/assets/css/video-booking.css', array(), '1.0.0');
wp_enqueue_script('video-booking-js', get_template_directory_uri() . '/video-booking/assets/js/video-booking.js', array('jquery'), '1.0.0', true);

// Localize script with AJAX data
wp_localize_script('video-booking-js', 'videoBooking', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('video_booking_nonce'),
    'site_url' => home_url(),
    'user_id' => get_current_user_id(),
    'is_logged_in' => is_user_logged_in(),
    'razorpay_key' => defined('RAZORPAY_KEY') ? RAZORPAY_KEY : '',
    'currency' => 'INR',
    'messages' => array(
        'login_required' => 'Please log in to access this feature',
        'cart_empty' => 'Your cart is empty',
        'processing' => 'Processing...',
        'error' => 'An error occurred. Please try again.',
        'success' => 'Success!'
    )
));

// Include the video booking cart template
if (file_exists(get_template_directory() . '/video-booking/templates/cart.php')) {
    include get_template_directory() . '/video-booking/templates/cart.php';
} else {
    // Fallback if template doesn't exist
    get_header();
?>
    <div class="container">
        <h1>Video Cart</h1>
        <p>Video booking system is not properly configured.</p>
    </div>
<?php
    get_footer();
}
