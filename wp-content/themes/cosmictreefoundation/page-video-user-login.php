<?php

/**
 * Template Name: Video User Login
 *
 * @package CosmicTreeFoundation
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Enqueue authentication scripts
wp_enqueue_script(
    'video-auth',
    get_template_directory_uri() . '/video-booking/assets/js/video-auth.js',
    array('jquery'),
    '1.0.0',
    true
);

wp_localize_script('video-auth', 'videoAuth', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('video_auth_nonce'),
    'login_url' => home_url('/video-user-login'),
    'register_url' => home_url('/video-user-register'),
    'account_url' => home_url('/my-recordings'),
    'is_logged_in' => is_user_logged_in(),
    'current_user' => is_user_logged_in() ? wp_get_current_user()->display_name : ''
));

// Include the video booking login template
if (file_exists(get_template_directory() . '/video-booking/templates/auth/login.php')) {
    include get_template_directory() . '/video-booking/templates/auth/login.php';
} else {
    // Fallback if template doesn't exist
    get_header();
?>
    <div class="container">
        <h1>Login</h1>
        <p>Video booking system is not properly configured.</p>
    </div>
<?php
    get_footer();
}
